import 'package:mastercookai/app/routes/routes.dart';
import 'package:mastercookai/core/data/models/recipe_response.dart';
import 'package:mastercookai/core/data/models/cookbook.dart';
import 'package:mastercookai/core/data/models/recipe_detail_response.dart';
import 'package:mastercookai/core/utils/routing/personal_routing_keys.dart';
import 'package:mastercookai/presentation/recipe/add_recipe_screen.dart';
import 'package:mastercookai/presentation/menus/bottom_nav.dart';
import 'package:mastercookai/presentation/cookbook/cookbook_details_screen.dart';
import 'package:mastercookai/presentation/cookbook/cookbook_screen.dart';
import 'package:mastercookai/presentation/recipe/edit_recipe_screen.dart';
import 'package:mastercookai/presentation/gpt/gpt_screen.dart';
import 'package:mastercookai/presentation/gpt/mobile/gpt_chat_screen.dart';
import 'package:mastercookai/presentation/home/<USER>';
import 'package:mastercookai/presentation/recipe/recipe_detail_screen.dart';
import 'package:mastercookai/presentation/shopping/shopping_list_screen.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:mastercookai/presentation/sync/mobile_ui/mobile_sync_screen.dart';

import '../../core/data/models/category_response.dart';
import '../../core/data/models/cuisines_response.dart';
import '../../core/utils/device_utils.dart';
import '../../core/utils/routing/routing_paths.dart';
import '../../presentation/cookbook/mobile/recipe_clipper_screen.dart';
import '../../presentation/mealplan/meal_plan_home_screen.dart';
import '../../presentation/menus/custom_bottom_nav_screen.dart';
import '../../presentation/menus/custom_bottom_nav_ipad.dart';
import '../../presentation/profile/myAccount.dart';
import '../../presentation/shopping/mobile/mobile_shopping_list_detail_screen.dart';
import '../../presentation/shopping/shopping_list_detail_screen.dart';

final personalRoutes = [
  StatefulShellRoute.indexedStack(
    branches: <StatefulShellBranch>[
      StatefulShellBranch(
        navigatorKey: shellNavigatorHome,
        routes: <RouteBase>[
          GoRoute(
            path: Routes.home,
            builder: (BuildContext context, GoRouterState state) =>
                HomeScreen(),
            routes: [
              GoRoute(
                path: Routes.gpt,
                parentNavigatorKey: rootNavigatorKey,
                builder: (context, state) => GptScreen(),
              ),
              GoRoute(
                path: 'gpt_mobile',
                parentNavigatorKey: rootNavigatorKey,
                builder: (context, state) => GptChatScreen(),
              ),
              GoRoute(
                path: Routes.meals,
                parentNavigatorKey: rootNavigatorKey,
                builder: (context, state) => MealPlanHomeScreen(),
              ),
              GoRoute(
                path: Routes.recipeCliper,
                parentNavigatorKey: rootNavigatorKey,
                builder: (context, state) => const RecipeClipperScreen(),
              ),

              GoRoute(
                path: Routes.sync,
                parentNavigatorKey: rootNavigatorKey,
                builder: (context, state) => const MobileSyncScreen(),
              ),
            ],
          ),
        ],
      ),
      StatefulShellBranch(
        navigatorKey: shellNavigatorCookbook,
        routes: <RouteBase>[
          GoRoute(
            path: Routes.cookbook,
            builder: (BuildContext context, state) => const CookbookScreen(),
            routes: [
              GoRoute(
                path: Routes.cookbook_detail,
                parentNavigatorKey: rootNavigatorKey,
                builder: (context, state) => CookbookDetailScreen(),
                routes: [
                  GoRoute(
                    path: 'recipeClipper',
                    parentNavigatorKey: rootNavigatorKey,
                    builder: (context, state) => const RecipeClipperScreen(),
                  ),
                  GoRoute(
                    path: 'recipeDetail/:id',
                    parentNavigatorKey: rootNavigatorKey,
                    builder: (context, state) {
                      final extras = state.extra as Map<String, dynamic>?;
                      final recipeList = extras?['recipeList'] as List<Recipe>?;
                      final selectedRecipeName =
                          extras?['recipeName'] as String?;
                      final selectedCookBookName =
                          extras?['cookBookName'] as String?;
                      final cookbookId = extras?['cookbookId'] as int;
                      final id = extras?['id'] as int;
                      return RecipeDetailScreen(
                        recipeId: id,
                        recipesList: recipeList!,
                        selectedRecipeName: selectedRecipeName ?? '',
                        cookbookId: cookbookId,
                        cookBookName: selectedCookBookName ?? '',
                      );
                    },
                    routes: [
                      GoRoute(
                        path: Routes.edit_recipe,
                        parentNavigatorKey: rootNavigatorKey,
                        builder: (context, state) {
                          final extras = state.extra as Map<String, dynamic>?;
                          final recipesList =
                              extras?['recipesList'] as List<Recipe>?;
                          final cookbookId = extras?['cookbookId'] as int;
                          final recipeId = extras?['recipeId'] as int;
                          final recipeDetails =
                              extras?['recipeDetails'] as RecipeDetails;
                          final categories =
                              extras?['categories'] as List<Categories>;
                          final cuisines =
                              extras?['cuisines'] as List<Cuisines>;
                          final initialStep = extras?['initialStep'] as int?;
                          return EditRecipeScreen(
                            recipeId: recipeId,
                            recipesList: recipesList!,
                            cookbookId: cookbookId,
                            recipeDetails: recipeDetails,
                            categories: categories,
                            cuisines: cuisines,
                            initialStep: initialStep,
                          );
                        },
                      ),
                    ],
                  ),
                  GoRoute(
                    path: Routes.add_recipe,
                    parentNavigatorKey: rootNavigatorKey,
                    builder: (context, state) {
                      final extra = state.extra;
                      Cookbook? selectedCookbook;
                      bool callFromClipper = false;
                      String clipperNote = ' ';
                      RecipeDetails? recipeDetails;

                      if (extra is Map) {
                        selectedCookbook =
                            extra['selectedCookbook'] as Cookbook?;
                        callFromClipper =
                            extra['callFromClipper'] as bool? ?? false;
                        clipperNote = extra['clipperNote'] as String;
                        recipeDetails =
                            extra['recipeDetails'] as RecipeDetails?;
                      } else if (extra is Cookbook) {
                        selectedCookbook = extra;
                        callFromClipper =
                            false; // Default to false if only Cookbook is passed
                      }

                      return AddRecipeScreen(
                        selectedCookbook: selectedCookbook,
                        callFromClipper: callFromClipper,
                        clipperNote: clipperNote,
                        recipeDetails: recipeDetails,
                      );
                    },
                  ),
                  // GoRoute(
                  //   path: Routes.add_recipe,
                  //   parentNavigatorKey: rootNavigatorKey,
                  //   builder: (context, state) {
                  //     final extra = state.extra;
                  //     return AddRecipeScreen(
                  //       selectedCookbook: extra is Cookbook ? extra : null,
                  //
                  //     );
                  //   },
                  // ),
                ],
              ),
            ],
          ),
        ],
      ),
      // StatefulShellBranch(
      //   navigatorKey: shellNavigatorMeals,
      //   routes: <RouteBase>[
      //     GoRoute(
      //       path: Routes.gpt,
      //       builder: (BuildContext context, GoRouterState state) =>
      //           GptScreen(),
      //     ),
      //   ],
      // ),
      StatefulShellBranch(
        navigatorKey: shellNavigatorShopping,
        routes: <RouteBase>[
          GoRoute(
            path: Routes.shopping,
            builder: (BuildContext context, GoRouterState state) =>
                ShoppingScreen(),
            routes: [
              GoRoute(
                parentNavigatorKey: rootNavigatorKey,
                path: Routes.shopping_detail,
                builder: (BuildContext context, GoRouterState state) =>
                    ShoppingListDetailScreen(),
              ),
              GoRoute(
                parentNavigatorKey: rootNavigatorKey,
                path: 'mobile_shopping_list_detail_screen',
                builder: (BuildContext context, GoRouterState state) =>
                    const MobileShoppingListDetail(),
              ),
            ],
          ),
        ],
      ),
      // StatefulShellBranch(
      //   navigatorKey: shellNavigatorTips,
      //   routes: <RouteBase>[
      //     GoRoute(
      //         path: Routes.tips,
      //         builder: (BuildContext context, GoRouterState state) =>
      //             const ComingSoonScreen()),
      //   ],
      // ),
      // StatefulShellBranch(
      //   navigatorKey: shellNavigatorMedia,
      //   routes: <RouteBase>[
      //     GoRoute(
      //       path: Routes.media,
      //       builder: (BuildContext context, GoRouterState state) =>
      //           const ComingSoonScreen(),
      //     ),
      //   ],
      // ),
      // StatefulShellBranch(
      //   navigatorKey: shellNavigatorFav,
      //   routes: <RouteBase>[
      //     GoRoute(
      //       path: Routes.recipeCliper,
      //       builder: (BuildContext context, GoRouterState state) =>
      //           const RecipeClipperScreen(),
      //     ),
      //   ],
      // ),
      // StatefulShellBranch(
      //   navigatorKey: shellNavigatorSync,
      //   routes: <RouteBase>[
      //     GoRoute(
      //       path:   Routes.sync,
      //       pageBuilder: (context, state) {
      //         return DialogPage(
      //           child: const SyncDialog(),
      //         );
      //       },
      //     ),
      //
      //   ],
      // ),
      // StatefulShellBranch(
      //   navigatorKey: shellNavigatorBackup,
      //   routes: <RouteBase>[
      //     GoRoute(
      //       path: Routes.backup,
      //       builder: (BuildContext context, GoRouterState state) =>
      //           const ComingSoonScreen(),
      //     ),
      //   ],
      // ),
      StatefulShellBranch(
        navigatorKey: shellNavigatorMyAccount,
        routes: <RouteBase>[
          GoRoute(
            path: Routes.myAccount,
            builder: (BuildContext context, GoRouterState state) =>
                const myAccount(),
          ),
        ],
      ),
      // StatefulShellBranch(
      //   navigatorKey: shellNavigatorRecipeClipper,
      //   routes: <RouteBase>[
      //     GoRoute(
      //       path: Routes.recipeCliper,
      //       builder: (BuildContext context, GoRouterState state) =>
      //           const ComingSoonScreen(),
      //     ),
      //   ],
      // ),
    ],
    builder: (context, state, navigationShell) {
      final deviceType = getDeviceType(context).name;
      if (deviceType == 'ipad' || deviceType == 'tablet') {
        return CustomBottomNavIpad(
          key: state.pageKey,
          navigationShell: navigationShell,
        );
      } else if (deviceType == 'mobile') {
        return CustomBottomNavScreen(
          key: state.pageKey,
          navigationShell: navigationShell,
        );
      } else {
        return BottomNavScreen(
          key: state.pageKey,
          navigationShell: navigationShell,
        );
      }
    },
  ),
];
