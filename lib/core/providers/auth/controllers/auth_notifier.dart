import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/core/data/request_query/register_request.dart';
import 'package:mastercookai/core/data/request_query/verifyotp_request.dart';
import 'package:mastercookai/presentation/auth/mobile/mobile_otp_screen.dart';
import 'package:mastercookai/presentation/home/<USER>/mobile_home_screen.dart';
import '../../../../presentation/auth/verfiy_otp_screen.dart';
import '../../../data/request_query/auth_request.dart';
import '../../../helpers/local_storage_service.dart';
import '../../../network/app_status.dart';
import '../../../network/base_notifier.dart';
import '../../../network/network_utils.dart';
import '../../../utils/Utils.dart';
import '../../../utils/device_utils.dart';

final authNotifierProvider =
    StateNotifierProvider<AuthNotifier, AppState<void>>(
  (ref) => AuthNotifier(ref),
);

class AuthNotifier extends BaseNotifier<void> {
  AuthNotifier(Ref ref) : super(ref, const AppState<void>()) {
    _initialize();
  }

  Future<void> _initialize() async {
    callDataService(
      repo.getPartner(),
      onStart: () => print('Loading partner...'),
      onSuccess: (response) =>
          print('Partner loaded: {${response.toString()}}'),
      onError: (e) => print('Error: $e'),
      onComplete: () => print('Partner load complete'),
    );
  }

  void setLoggedIn(bool isLoggedIn) {
    state = state.copyWith(
      status: isLoggedIn ? AppStatus.success : AppStatus.idle,
    );
  }

  Future<void> authenticate(
      BuildContext context, String username, String password) async {
    callDataService(
      repo.login(AuthQueryParam(
        username: username,
        password: password,
      )),
      onStart: () => state = state.copyWith(status: AppStatus.loading),
      onSuccess: (response) async =>
          await _handleSuccessResponse(context, response, username, password),
      onError: (e) {
        state = state.copyWith(
          status: AppStatus.error,
          errorMessage: e.message ?? 'Login failed',
        );
        Utils()
            .showFlushbar(context, message: state.errorMessage!, isError: true);
      },
    );
  }

  Future<void> register(BuildContext context, String username, String password,
      String useremail, String userType,
      {bool isResend = false}) async {
    callDataService(
      repo.register(RegisterRequest(
        username: username,
        password: password,
        useremail: useremail,
        userType: userType,
      )),
      onStart: () => state = state.copyWith(status: AppStatus.loading),
      onSuccess: (response) async => await _handleRegisterSuccessResponse(
        context,
        response,
        useremail,
        username,
        password,
        userType,
        isResend: isResend,
      ),
      onError: (e) {
        state = state.copyWith(
          status: AppStatus.error,
          errorMessage: e.message ?? 'Registration failed',
        );
        Utils()
            .showFlushbar(context, message: state.errorMessage!, isError: true);
      },
    );
  }

  Future<void> verifyotp(
      BuildContext context, String useremail, String otp) async {
    callDataService(
      repo.verifyotp(VerifyotpRequest(
        useremail: useremail,
        otp: otp,
      )),
      onStart: () => state = state.copyWith(status: AppStatus.creating),
      onSuccess: (response) async {
        state = state.copyWith(status: AppStatus.createSuccess);
        //  context.go('/login');
        if (context.mounted) {
          await localStorage.saveLoginData(
            loginModel: response,
            email: useremail,
          );
          context.go('/home');
        }
      },
      onError: (e) {
        state = state.copyWith(
          status: AppStatus.createError,
          errorMessage: e.message ?? 'Registration failed',
        );
        Utils()
            .showFlushbar(context, message: state.errorMessage!, isError: true);
      },
    );
  }

  Future<void> _handleSuccessResponse(BuildContext context, dynamic response,
      String username, String password) async {
    state = state.copyWith(status: AppStatus.success);
    if (context.mounted) {
      await localStorage.saveLoginData(
        loginModel: response,
        email: username,
        password: password,
      );
      context.go('/home');
    }
  }

  // Future<void> _handleRegisterSuccessResponse(
  //     BuildContext context, dynamic response, String email) async {
  //   state = state.copyWith(status: AppStatus.success);
  //   Navigator.push(
  //     context,
  //     if (getDeviceType(context).name == 'mobile')
  //       MaterialPageRoute(builder: (context) => MobileOtpScreen(email: email)),
  //     else
  //       MaterialPageRoute(builder: (context) => VerfiyOtpScreen(email: email)),
  //   );
  // }  

  Future<void> _handleRegisterSuccessResponse(
    BuildContext context,
    dynamic response,
    String email,
    String fullName,
    String password,
    String userType, {
    bool isResend = false,
  }) async {
    state = state.copyWith(status: AppStatus.success);
    if (isResend) {
      // Do not navigate, just show a success message or update state
      print("OTP Resent successfully");
      return;
    }
    final route = getDeviceType(context).name == 'mobile'
        ? MaterialPageRoute(
            builder: (context) => MobileOtpScreen(
              email: email,
              fullName: fullName,
              password: password,
              userType: userType,
            ),
          )
        : MaterialPageRoute(
            builder: (context) => VerfiyOtpScreen(
              email: email,
              fullName: fullName,
              password: password,
              userType: userType,
            ),
          );

    Navigator.push(context, route);
  }

  Future<void> logout(BuildContext context) async {
    callDataService(
      repo.logout(),
      onStart: () => state = state.copyWith(status: AppStatus.loading),
      onSuccess: (response) async =>
          await _handleSuccessLogoutResponse(context, response),
      onError: (e) {
        state = state.copyWith(
          status: AppStatus.error,
          errorMessage: e.message ?? 'Login failed',
        );
        Utils().showSnackBar(context, state.errorMessage!);
      },
    );
  }

  Future<void> _handleSuccessLogoutResponse(
      BuildContext context, dynamic response) async {
    state = state.copyWith(status: AppStatus.success);
    if (context.mounted) {
      // await localStorage.saveLoginData(
      //   loginModel: response
      // );
      context.go('/splash');
      print('Logout button pressed!');
      final localStorage = ref.read(localStorageProvider);
      await localStorage.clearLoginData();
    }
  }

  void reset() {
    state = const AppState<void>();
  }

  void signUp(
      BuildContext context, String trim, String trim2, String selectedType) {
    state = state.copyWith(
      status: AppStatus.success,
      errorMessage:
          'Sign up successful for $trim $trim2 with type $selectedType',
    );
  }
}
