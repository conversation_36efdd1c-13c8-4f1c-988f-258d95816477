import 'dart:io';

import 'package:flutter/material.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/core/helpers/app_constant.dart';
import 'package:mastercookai/core/helpers/media_picker_service.dart';
import 'package:mastercookai/core/utils/Utils.dart';
import 'package:mastercookai/core/data/models/cookbook.dart';
import 'package:mastercookai/core/data/request_query/fetch_cookbook_request.dart';
import 'package:mastercookai/core/network/network_utils.dart';
import 'package:mastercookai/core/widgets/image_cropper/image_cropper.dart';
import '../../app/imports/packages_imports.dart';
import '../../presentation/cookbook/mobile/sub_views/cookbook_bottom_sheet_content.dart';
import '../data/models/recipe_detail_response.dart';
import '../network/app_status.dart';
import '../network/base_notifier.dart';
import 'package:path/path.dart' as path;

final cookbookNotifierProvider =
    StateNotifierProvider<CookbookNotifier, AppState<List<Cookbook>>>(
  (ref) => CookbookNotifier(ref),
);

class CookbookNotifier extends BaseNotifier<List<Cookbook>> {
  CookbookNotifier(Ref ref) : super(ref, const AppState<List<Cookbook>>()) {
    _initialize();
  }

  RecipeDetails? _clipperResponse; // Store RecipeDetails

  RecipeDetails? get clipperResponse => _clipperResponse;

  // Method to set clipper response
  void setClipperResponse(RecipeDetails recipeDetails) {
    _clipperResponse = recipeDetails;
  }

  void _initialize() {
    // Initial fetch of cookbooks
    callDataService(
      repo.fetchCookbooks(FetchCookbookRequest(
        pageNumber: 1,
        pageSize: 10,
        includeRecipes: true,
        itemSort: 'Newest',
      )),
      onStart: () => state = state.copyWith(status: AppStatus.loading),
      onSuccess: (response) => _handleSuccessResponse(response, false),
      onError: (e) => state = state.copyWith(
        status: AppStatus.error,
        errorMessage: e.message ?? 'Failed to fetch cookbooks',
      ),
      onComplete: () => print('Cookbook fetch complete'),
    );
  }

  /// POST - Create new cookbook
  Future<void> createCookbook(BuildContext context, String name) async {
    callDataService(
      repo.createCookbook(name),
      onStart: () => state = state.copyWith(status: AppStatus.creating),
      onSuccess: (response) async {
        if (response.success == false) {
          if (context.mounted) {
            Utils().showFlushbar(context,
                message: response.message?.error?.first ??
                    'Failed to create cookbook',
                isError: true);
          }
          // Close the dialog if open
          state = state.copyWith(
              status: AppStatus.createError,
              errorMessage: response.message?.error?.first);

          return;
        }
        state = state.copyWith(status: AppStatus.createSuccess);
      },
      onError: (e) {
        state = state.copyWith(
            status: AppStatus.createError, errorMessage: e.message);
        if (context.mounted) {
          Utils().showFlushbar(context,
              message: state.errorMessage!, isError: true);
        }
      },
    );
  }

  /// GET - Fetch cookbooks with pagination
  Future<bool> fetchCookbooks({
    required BuildContext context,
    bool loadMore = false,
    String? search,
    String? sort,
  }) async {
    if (loadMore && (!state.hasMore || state.status == AppStatus.loadingMore)) {
      return false;
    }

    final currentPage = loadMore ? state.currentPage + 1 : 1;
    bool isSuccess = false;

    await callDataService(
      repo.fetchCookbooks(FetchCookbookRequest(
        pageNumber: currentPage,
        pageSize: 10,
        includeRecipes: true,
        itemSort: sort ?? 'Newest',
        search: search,
      )),
      onStart: () {
        state = state.copyWith(
          status: loadMore ? AppStatus.loadingMore : AppStatus.loading,
          data: loadMore ? state.data : [],
          hasMore: loadMore ? state.hasMore : true,
          currentPage: currentPage,
        );
      },
      onSuccess: (response) {
        _handleSuccessResponse(response, loadMore);
        isSuccess = true;
      },
      onError: (e) {
        state = state.copyWith(
          status: AppStatus.error,
          errorMessage: e.message ?? 'Failed to fetch cookbooks',
        );
        if (context.mounted) {
          Utils().showFlushbar(context,
              message: state.errorMessage!, isError: true);
        }
        isSuccess = false;
      },
      onComplete: () => print('Cookbook fetch completed'),
    );

    return isSuccess;
  }

  /// DELETE - Delete cookbook
  Future<bool> deleteCookbook(BuildContext context, String id) async {
    final bool? confirmed = await Utils().showCommonConfirmDialog(
      context: context,
      title: 'Delete Cookbook',
      subtitle: 'Are you sure you want to delete this cookbook?',
      confirmText: 'Delete',
      cancelText: 'Cancel',
    );
    if (confirmed != true) {
      print('Deletion cancelled by user');
      return false; // User cancelled deletion
    }

    bool success = false;
    try {
      await callDataService(
        repo.deleteCookbook(id),
        onStart: () {
          print('Starting deletion for cookbook ID: $id');
          state = state.copyWith(status: AppStatus.loading);
        },
        onSuccess: (response) async {
          print('API response: $response, success: ${response.success}');

          if (response.success == true) {
            // Relaxed null check
            success = true;
            print('Deletion successful, refreshing cookbooks');
            state = state.copyWith(status: AppStatus.success);
            await fetchCookbooks(context: context); // Refresh cookbooks
            if (context.mounted) {
              Utils().showFlushbar(
                context,
                message: 'Cookbook deleted successfully',
                isError: false,
              );
            }
          } else {
            print('API returned success: false');
            state = state.copyWith(
              status: AppStatus.error,
              errorMessage: 'Failed to delete cookbook: Invalid response',
            );
            if (context.mounted) {
              Utils().showFlushbar(
                context,
                message: state.errorMessage!,
                isError: true,
              );
            }
            success = false;
          }
        },
        onError: (e) {
          print('Error during deletion: ${e.message}');
          state = state.copyWith(
            status: AppStatus.error,
            errorMessage: e.message ?? 'Failed to delete cookbook',
          );
          if (context.mounted) {
            Utils().showFlushbar(
              context,
              message: state.errorMessage!,
              isError: true,
            );
          }
          success = false;
        },
        onComplete: () {
          print('Deletion process completed, success: $success');
        },
      );
    } catch (e) {
      print('Unexpected error in deleteCookbook: $e');
      state = state.copyWith(
        status: AppStatus.error,
        errorMessage: 'Unexpected error: $e',
      );
      if (context.mounted) {
        Utils().showFlushbar(
          context,
          message: state.errorMessage!,
          isError: true,
        );
      }
      success = false;
    }

    print('Returning success: $success');
    return success;
  }

  Future<void> updateCookbook(BuildContext context,
      {required String id,
      required String name,
      required File filePath,
      required bool callFromUpdate}) async {
    callDataService(
      repo.updateCookbook(id, name, filePath),
      onStart: () => state = state.copyWith(status: AppStatus.updating),
      onSuccess: (response) async {
        state = state.copyWith(status: AppStatus.updateSuccess);
        if (callFromUpdate) {
          await fetchCookbooks(context: context);
        }
      },
      onError: (e) {
        state = state.copyWith(
          status: AppStatus.updateError,
          errorMessage: e.message ?? 'Failed to delete cookbook',
        );
        if (context.mounted) {
          Utils().showFlushbar(context,
              message: state.errorMessage!, isError: true);
        }
      },
      onComplete: () => print('Deletion process completed'),
    );
  }

  void _handleSuccessResponse(GetCookbooksResponse response, bool loadMore) {
    final result = response.data;

    state = state.copyWith(
      status: result.cookbooks.isEmpty && !loadMore
          ? AppStatus.empty
          : AppStatus.success,
      data: loadMore
          ? [...state.data ?? [], ...result.cookbooks]
          : result.cookbooks,
      hasMore: result.cookbooks.length == 10,
      currentPage: state.currentPage,
      totalItems: result.totalRecords,
      errorMessage: null,
    );
  }

  Future<void> pickImage(
      BuildContext context, WidgetRef ref, String id, String name) async {
    final file = await MediaPickerService.pickSingleImage();
    if (file != null) {
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => ImageCropper(
            pickedImage: file,
            showCropPresets: true,
            showGridLines: false,
            useDelegate: true,
            enableFreeformCrop: true,
            onImageCropped: (File? croppedImageFile) {
              if (croppedImageFile != null) {
                updateCookbook(
                  context,
                  id: id,
                  name: name,
                  filePath: croppedImageFile,
                  callFromUpdate: true,
                );
              } else {
                print("Cropped image is null!");
              }
            },
          ),
        ),
      );
    }
  }

  Future<bool> recipeClipper(
    BuildContext context,
    String url,
    Cookbook? selectedCookbook,
  ) async {
    bool success = false;
    try {
      await callDataService(
        repo.clipperRecipe(url: url),
        onStart: () {
          state = state.copyWith(status: AppStatus.loading);
        },
        onSuccess: (response) async {
          if (response.success == true) {
            // Relaxed null check
            _clipperResponse = response.data?.recipeDetails;
            success = true;
            state = state.copyWith(status: AppStatus.success);
            await fetchCookbooks(context: context); // Refresh cookbooks
            if (context.mounted) {
              Utils().showFlushbar(
                context,
                message: response.message?.general?.first ?? '',
                isError: false,
              );
            } else {
              success = false;
              print('API returned success: false');
              state = state.copyWith(
                status: AppStatus.createError,
                errorMessage: 'Failed to delete cookbook: Invalid response',
              );
              if (context.mounted) {
                Utils().showFlushbar(
                  context,
                  message: response.message?.error?.first ??
                      'Failed to import cookbook',
                  isError: true,
                );
              }
            }
          }
        },
        onError: (e) {
          state = state.copyWith(
            status: AppStatus.error,
            errorMessage: e.message ?? 'Failed to delete cookbook',
          );
          if (context.mounted) {
            Utils().showFlushbar(
              context,
              message: state.errorMessage!,
              isError: true,
            );
          }
          success = false;
        },
        onComplete: () {
          print('Deletion process completed, success: $success');
        },
      );
    } catch (e) {
      print('Unexpected error in deleteCookbook: $e');
      state = state.copyWith(
        status: AppStatus.error,
        errorMessage: 'Unexpected error: $e',
      );
      if (context.mounted) {
        Utils().showFlushbar(
          context,
          message: state.errorMessage!,
          isError: true,
        );
      }
      success = false;
    }

    print('Returning success: $success');
    return success;
  }

  Future<bool> importCookBook(BuildContext context, File file, int cookBookId,
      String cookBookName) async {
    bool success = false;
    try {
      final fileName = path.basenameWithoutExtension(file.path);

      showLeftLoader(fileName, cookBookName);
      await callDataService(
        repo.importCookBook(file, cookBookId),
        onStart: () {
          state = state.copyWith(status: AppStatus.creating);
        },
        onSuccess: (response) async {
          if (response.success == true) {
            success = true;
            state = state.copyWith(status: AppStatus.createSuccess);
            // Refresh cookbooks
            if (context.mounted) {
              Utils().showFlushbar(
                context,
                message:
                    response.message?.general?.first ?? 'Imported successfully',
                isError: false,
              );
            }
          } else {
            success = false;
            print('API returned success: false');
            state = state.copyWith(
              status: AppStatus.createError,
              errorMessage: 'Failed to delete cookbook: Invalid response',
            );
            if (context.mounted) {
              Utils().showFlushbar(
                context,
                message: response.message?.error?.first ??
                    'Failed to import cookbook',
                isError: true,
              );
            }
          }
        },
        onError: (e) {
          success = false;
          print('Error during deletion: ${e.message}');
          state = state.copyWith(
            status: AppStatus.createError,
            errorMessage: e.message ?? 'Failed to delete cookbook',
          );
          if (context.mounted) {
            Utils().showFlushbar(
              context,
              message: state.errorMessage!,
              isError: true,
            );
          }
        },
        onComplete: () {},
      );
    } catch (e) {
      success = false;
      state = state.copyWith(
        status: AppStatus.createError,
        errorMessage: 'Unexpected error: $e',
      );
    } finally {
      hideLoader();
    }
    return success;
  }

  Future<void> showCookbookBottomSheet({
    required BuildContext context,
    required WidgetRef ref,
    bool callFromUpdate = false,
    String? cookbookId,
    String? cookbookName,
    VoidCallback? onSuccess,
  }) {
    var isApiCall = false;
    return showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      isDismissible: false,
      builder: (context) {
        // Calculate keyboard height
        final keyboardHeight = MediaQuery.of(context).viewInsets.bottom;
        final screenHeight = MediaQuery.of(context).size.height + 100;

        // Adjust sizes based on keyboard presence
        final baseInitialSize = 0.37;
        final baseMaxSize = 0.4;

        // When keyboard is visible, increase the max size to accommodate it
        final adjustedMaxSize = keyboardHeight > 0
            ? (baseMaxSize + (keyboardHeight / screenHeight)).clamp(0.4, 0.9)
            : baseMaxSize;

        final adjustedInitialSize = keyboardHeight > 0
            ? adjustedMaxSize
            : baseInitialSize;

        return DraggableScrollableSheet(
          initialChildSize: adjustedInitialSize,
          minChildSize: 0.3,
          maxChildSize: adjustedMaxSize,
          expand: false,
          builder: (_, controller) {
            return CookbookBottomSheetContent(
              title: callFromUpdate ? 'Edit CookBook' : 'Create Cookbook',
              hintText: 'Enter cookbook name',
              successText: callFromUpdate
                  ? 'Cookbook updated successfully!'
                  : 'Cookbook created successfully!',
              buttonText: callFromUpdate ? 'Update' : 'Create Now',
              successButtonText: 'Go to Cookbooks',
              callFromUpdate: callFromUpdate,
              cookbookId: cookbookId,
              cookbookName: cookbookName,
              onCreate: (name, context, ref) {
                isApiCall = true;
              },
              onSuccess: onSuccess,
              scrollController: controller,
            );
          },
        );
      },
    ).whenComplete(() {
      // Reset state when bottom sheet is closed
      if (isApiCall) {
        ref
            .read(cookbookNotifierProvider.notifier)
            .fetchCookbooks(context: context);
      }
    });
  }

  @override
  void reset() {
    state = const AppState<List<Cookbook>>();
  }

  void resetToIdle() {
    state = state.copyWith(status: AppStatus.idle, errorMessage: null);
  }
}
