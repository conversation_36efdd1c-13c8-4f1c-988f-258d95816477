import 'dart:convert';
import 'dart:io';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../data/models/direction_step.dart';
import '../../data/models/recipe_detail_response.dart';


// Provider
final directionStepsProvider =
StateNotifierProvider<DirectionStepsNotifier, List<DirectionStep>>((ref) {
  return DirectionStepsNotifier();
});

class DirectionStepsNotifier extends StateNotifier<List<DirectionStep>> {
  DirectionStepsNotifier() : super([]);

  List<File?> _mediaFiles = []; // Track media files, allowing null entries

  List<File>? get mediaFiles => _mediaFiles.whereType<File>().toList();

  void addStep() {
    state = [
      ...state,
      DirectionStep(
        title: "",
        description: "",
        mediaFile: null,
        mediaFileName: null,
        mediaFileId: null,
      ),
    ];
    _mediaFiles = [..._mediaFiles, null];
    print("Added step, new state: ${state.map((e) => e.toJson()).toList()}");
  }

  void updateStep(
      int index, {
        String? title,
        String? description,
        File? mediaFile,
        bool? removeMediaFile,
        String? mediaFileName,
        bool? removeMediaFileName,
        int? mediaFileId,
        String? imageUrl,
      }) {
    if (index < 0 || index >= state.length) {
      print("Invalid index $index for updateStep");
      return;
    }

    final current = state[index];
    bool hasChanges = false;

    final updatedFields = <String, dynamic>{};

    if (title != null) {
      updatedFields['title'] = title;
      hasChanges = true;
    }
    if (description != null) {
      updatedFields['description'] = description;
      hasChanges = true;
    }
    if (mediaFile != null) {
      updatedFields['mediaFile'] = mediaFile;
      updatedFields['mediaFileName'] = mediaFileName;
      updatedFields['mediaFileId'] = null; // Clear MediaFileId for new media
      hasChanges = true;
    }
    if (removeMediaFile == true) {
      updatedFields['mediaFile'] = null;
      updatedFields['mediaFileName'] = null;
      updatedFields['mediaFileId'] = null; // Clear MediaFileId on removal
      hasChanges = true;
    }
    if (mediaFileName != null) {
      updatedFields['mediaFileName'] = mediaFileName;
      updatedFields['mediaFileId'] = null; // Clear MediaFileId if MediaFileName is set
      hasChanges = true;
    }
    if (removeMediaFileName == true) {
      updatedFields['mediaFileName'] = null;
      updatedFields['mediaFileId'] = null; // Clear MediaFileId on removal
      hasChanges = true;
    }
    if (mediaFileId != null) {
      updatedFields['mediaFileId'] = mediaFileName != null && mediaFileName!.isNotEmpty
          ? null
          : mediaFileId; // Respect MediaFileName rule
      hasChanges = true;
    }
    if (imageUrl != null) {
      updatedFields['imageUrl'] = imageUrl;
      hasChanges = true;
    }

    if (hasChanges) {
      final updatedStep = DirectionStep(
        title: updatedFields['title'] ?? current.title,
        description: updatedFields['description'] ?? current.description,
        mediaFile: updatedFields['mediaFile'] ?? current.mediaFile,
        mediaFileName: updatedFields['mediaFileName'] ?? current.mediaFileName,
        mediaFileId: updatedFields['mediaFileId'] ?? current.mediaFileId,
        imageUrl: updatedFields['imageUrl'] ?? current.imageUrl,
      );

      state[index] = updatedStep;
      _mediaFiles[index] = updatedFields['mediaFile'] ?? current.mediaFile;
      state = [...state]; // Trigger rebuild
      print("Updated step at index $index: ${updatedStep.toJson()}");
    } else {
      print("No changes to update for step at index $index");
    }
  }

  void setDirections(List<Directions> backendDirections) {
    state = backendDirections.map((direction) {
      var id=direction.mediaFileId;

      return DirectionStep(
         title: direction.title ?? "",
          description: direction.description ?? "",
          imageUrl: direction.mediaUrl ?? '',
          mediaFileId: id,
      );
    }).toList();
   _mediaFiles = List.filled(state.length, null);
    if (state.isEmpty) {
      state = [DirectionStep(title: "", description: "",)];
      _mediaFiles = [null];
    }
    print("Set directions: ${state.map((e) => e.toJson()).toList()}");
  }

  void removeStep(int index) {
  //  if (state.length <= 1) {
  //     print("Cannot remove step at index $index: at least one step required");
  //     return;
  //  }
    final list = [...state]..removeAt(index);
    _mediaFiles = [..._mediaFiles]..removeAt(index);
    state = list;
    print(
        "Removed step at index $index, new state: ${state.map((e) => e.toJson()).toList()}");
  }

  void removeMediaFile(int index) {
    if (index < 0 || index >= state.length) {
      print("Invalid index $index for removeMediaFile");
      return;
    }
    final current = state[index];
    final updatedStep = DirectionStep(
      title: current.title,
      description: current.description,
      imageUrl: '',
      mediaFile: null,
      mediaFileName: null,
      mediaFileId: null, // Explicitly set mediaFileId to null
    );
    state[index] = updatedStep;
    _mediaFiles[index] = null;
    state = [...state]; // Trigger rebuild
    print("Removed media for step at index $index: ${updatedStep.toJson()}");
  }

  void resetWithDefaults({int defaultCount = 3}) {
    clearDirections();
    for (int i = 0; i < defaultCount; i++) {
      state = [...state, DirectionStep(title: "", description: "")];
      _mediaFiles = [..._mediaFiles, null];
    }
    print(
        "Reset with $defaultCount empty steps. New state: ${state.map((e) => e.toJson()).toList()}");
  }

  void clearDirections() {
    state = [];
    _mediaFiles = [];
    print("Cleared directions. New state: $state");
  }

  String get directionsJson {
    // Filter out empty objects and encode the remaining ones
    final validSteps = state
        .map((e) => e.toJson())
        .where((json) => json.isNotEmpty)
        .toList();
    return jsonEncode(validSteps);
  }
}