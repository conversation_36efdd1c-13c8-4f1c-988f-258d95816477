// lib/core/providers/shopping/pantry_notifier.dart
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mastercookai/core/data/models/pantry_response.dart';
import 'package:mastercookai/core/data/partner_api.dart';
import 'package:mastercookai/core/data/request_query/paginantion_request.dart';
import 'package:mastercookai/core/helpers/media_picker_service.dart';
import 'package:mastercookai/core/utils/Utils.dart';
import 'package:mastercookai/core/widgets/image_cropper/image_cropper.dart';
import '../../network/app_status.dart';
import '../../network/base_notifier.dart';
import '../../network/network_utils.dart';

final pantryNotifierProvider =
    StateNotifierProvider<PantryNotifier, AppState<PantryResponseData>>(
  (ref) => PantryNotifier(ref),
);

class PantryNotifier extends BaseNotifier<PantryResponseData> {
  PantryNotifier(Ref ref) : super(ref, const AppState<PantryResponseData>()) {
    _initialize();
  }

  void _initialize() {
    fetchPantryLists();
  }

  void showLoader() {
    // Implementation for showing loader
  }

  void hideLoader() {
    // Implementation for hiding loader
  }

  /// Fetch pantry lists with pagination
  Future<bool> fetchPantryLists({
    BuildContext? context,
    bool loadMore = false,
    String? search,
  }) async {
    if (loadMore && (!state.hasMore || state.status == AppStatus.loadingMore)) {
      return false;
    }

    final currentPage = loadMore ? state.currentPage + 1 : 1;
    bool isSuccess = false;

    final paginationParam = PaginationQueryParam(
      pageNumber: currentPage,
      pageSize: 10,
    );

    if (search != null && search.isNotEmpty) {
      paginationParam.search = search;
    }

    await callDataService(
      ref.read(partnerApiProvider).getPantryList(paginationParam),
      onStart: () => state = state.copyWith(
        status: loadMore ? AppStatus.loadingMore : AppStatus.loading,
        data: loadMore ? state.data : null,
        hasMore: loadMore ? state.hasMore : true,
        currentPage: currentPage,
      ),
      onSuccess: (response) {
        _handleSuccessResponse(response, loadMore);
        isSuccess = true;
      },
      onError: (e) {
        final errorMessage = _handleError(e);
        state = state.copyWith(
          status: AppStatus.error,
          errorMessage: errorMessage,
        );
        if (context?.mounted == true) {
          Utils().showFlushbar(context!, message: errorMessage, isError: true);
        }
        isSuccess = false;
      },
      onComplete: () => {},
    );

    return isSuccess;
  }

  /// Update Pantry
  Future<void> updatePantry(
    BuildContext context, {
    required String id,
    required String name,
    required File filePath,
    required bool callFromUpdate,
  }) async {
    await callDataService(
      ref.read(partnerApiProvider).updatePantry(id, name, filePath),
      onStart: () {
        showLoader();
        state = state.copyWith(status: AppStatus.updating);
      },
      onSuccess: (response) async {
        hideLoader();
        state = state.copyWith(status: AppStatus.updateSuccess);
        if (callFromUpdate && context.mounted) {
          await fetchPantryLists(context: context);
        }
        if (callFromUpdate) {
          Navigator.pop(context);
        }
      },
      onError: (e) {
        hideLoader();
        final errorMessage = _handleError(e);
        state = state.copyWith(
          status: AppStatus.updateError,
          errorMessage: errorMessage,
        );
        if (context.mounted) {
          Utils().showFlushbar(context, message: errorMessage, isError: true);
        }
      },
      onComplete: () => {},
    );
  }

  /// Create new pantry list
  Future<void> createPantryList(BuildContext context, String name) async {
    await callDataService(
      ref.read(partnerApiProvider).createPantryList(name),
      onStart: () => state = state.copyWith(status: AppStatus.creating),
      onSuccess: (response) async {
        if (response.success == false) {
          // Handle error response from API
          String errorMessage = 'Failed to create pantry list';

          if (response.message?.error != null) {
            // Handle error field - it can be List<String> or dynamic
            if (response.message!.error is List) {
              final errorList = response.message!.error as List;
              if (errorList.isNotEmpty) {
                errorMessage = errorList.first.toString();
              }
            } else if (response.message!.error is String) {
              errorMessage = response.message!.error.toString();
            }
          } else if (response.message?.general?.isNotEmpty == true) {
            errorMessage = response.message!.general!.first;
          }

          state = state.copyWith(
            status: AppStatus.createError,
            errorMessage: errorMessage,
          );
          if (context.mounted) {
            Utils().showFlushbar(context, message: errorMessage, isError: true);
          }
          return;
        }
        state = state.copyWith(status: AppStatus.createSuccess);
        await fetchPantryLists(context: context);
      },
      onError: (e) {
        state = state.copyWith(
          status: AppStatus.createError,
          errorMessage: e.message,
        );
        if (context.mounted) {
          Utils().showFlushbar(context, message: e.message, isError: true);
        }
      },
      onComplete: () => {},
    );
  }

  /// Delete pantry list
  Future<(AppStatus, String?)> deletePantryList(
      BuildContext context, String id) async {
    state = state.copyWith(status: AppStatus.loading);

    try {
      showLoader();
      final repositoryImpl = ref.read(partnerApiProvider);
      final response = await repositoryImpl.deletePantryList(id);

      if (response.success == true) {
        state = state.copyWith(status: AppStatus.success);
        if (context.mounted) {
          Utils().showFlushbar(context,
              message:
                  response.message?.general?.first ?? 'Deleted successfully',
              isError: false);

          await fetchPantryLists(context: context);
        }
        return (AppStatus.success, null);
      } else {
        final errorMessage = response.message?.general?.isNotEmpty == true
            ? response.message!.general!.first
            : 'Failed to delete pantry list';
        state = state.copyWith(
          status: AppStatus.error,
          errorMessage: errorMessage,
        );
        if (context.mounted) {
          Utils().showFlushbar(context, message: errorMessage, isError: true);
        }
        return (AppStatus.error, errorMessage);
      }
    } catch (e) {
      final errorMessage = _handleError(e);
      state = state.copyWith(
        status: AppStatus.error,
        errorMessage: errorMessage,
      );
      if (context.mounted) {
        Utils().showFlushbar(context, message: errorMessage, isError: true);
      }
      return (AppStatus.error, errorMessage);
    } finally {
      hideLoader();
    }
  }

  /// Pick image for pantry list cover
  Future<void> pickImage(
    BuildContext context,
    WidgetRef ref,
    String id,
    String name,
  ) async {
    final file = await MediaPickerService.pickSingleImage();
    if (file != null && context.mounted) {
      final result = await Navigator.of(context).push<File?>(
        MaterialPageRoute(
          builder: (context) => ImageCropper(
            pickedImage: file,
            showCropPresets: true,
            showGridLines: false,
            useDelegate: false, // Changed to false to get the file back
            enableFreeformCrop: true,
          ),
        ),
      );

      // Handle the cropped image result
      if (result != null && context.mounted) {
        await updatePantry(
          context,
          id: id,
          name: name,
          filePath: result,
          callFromUpdate: true,
        );
      }
    }
  }

  void _handleSuccessResponse(PantryResponse response, bool loadMore) {
    final result = response.data;
    state = state.copyWith(
      status: result!.pantries!.isEmpty && !loadMore
          ? AppStatus.empty
          : AppStatus.success,
      data: loadMore
          ? PantryResponseData(
              pantries: [...state.data?.pantries ?? [], ...result.pantries!],
              totalRecords: result.totalRecords,
              totalPageCount: result.totalPageCount,
            )
          : result,
      hasMore: result.pantries!.length == 10,
      currentPage: state.currentPage,
      totalItems: result.totalPageCount!.toInt(),
      errorMessage: null,
    );
  }

  String _handleError(dynamic error) {
    if (error is Exception) {
      return error.toString().replaceAll('Exception: ', '');
    } else {
      return 'An unexpected error occurred: ${error.toString()}';
    }
  }

  void reset() {
    state = const AppState<PantryResponseData>();
  }

  void resetToIdle() {
    state = state.copyWith(status: AppStatus.idle, errorMessage: null);
  }
}
