import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';

class ImageCompressionUtils {
  /// Compresses an image to the target file size in KB using a fast, single-pass approach
  /// Returns a File with the compressed image
  static Future<File> compressImageToSize(
    ui.Image image, {
    required int maxSizeKB,
    String? customFileName,
    Function(String)? onProgress,
  }) async {
    final tempDir = await getTemporaryDirectory();
    final fileName = customFileName ??
        'compressed_image_${DateTime.now().millisecondsSinceEpoch}.png';
    final tempFile = File('${tempDir.path}/$fileName');

    onProgress?.call('Calculating optimal size...');

    // For 250KB target, we want roughly 500x500 pixels or less
    // This is based on typical PNG compression ratios
    final maxDimension = (maxSizeKB * 2).clamp(300, 800); // Scale based on target size

    int targetWidth = image.width;
    int targetHeight = image.height;

    // Scale down if either dimension is too large
    if (image.width > maxDimension || image.height > maxDimension) {
      final scale = maxDimension / (image.width > image.height ? image.width : image.height);
      targetWidth = (image.width * scale).round();
      targetHeight = (image.height * scale).round();
    }

    // Ensure minimum size
    targetWidth = targetWidth.clamp(200, image.width);
    targetHeight = targetHeight.clamp(200, image.height);

    debugPrint('Resizing from ${image.width}x${image.height} to ${targetWidth}x$targetHeight');
    onProgress?.call('Resizing image...');

    // Resize image
    final resizedImage = await _resizeImage(image, targetWidth, targetHeight);

    onProgress?.call('Compressing image...');
    final compressedBytes = await _imageToBytes(resizedImage);

    final finalSizeKB = compressedBytes.length / 1024;
    debugPrint('Final compressed size: ${finalSizeKB.toStringAsFixed(1)} KB');

    onProgress?.call('Saving image...');
    await tempFile.writeAsBytes(compressedBytes);

    return tempFile;
  }

  /// Converts ui.Image to bytes in PNG format
  static Future<Uint8List> _imageToBytes(ui.Image image) async {
    final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
    if (byteData == null) {
      throw Exception('Failed to convert image to bytes');
    }
    return byteData.buffer.asUint8List();
  }

  /// Resizes an image to the specified dimensions
  static Future<ui.Image> _resizeImage(ui.Image originalImage, int width, int height) async {
    final recorder = ui.PictureRecorder();
    final canvas = Canvas(recorder);
    
    // Use medium quality for better performance vs quality balance
    final paint = Paint()..filterQuality = FilterQuality.medium;
    
    canvas.drawImageRect(
      originalImage,
      Rect.fromLTWH(0, 0, originalImage.width.toDouble(), originalImage.height.toDouble()),
      Rect.fromLTWH(0, 0, width.toDouble(), height.toDouble()),
      paint,
    );
    
    final picture = recorder.endRecording();
    return await picture.toImage(width, height);
  }

  /// Validates if a file size is within the acceptable range
  static Future<bool> isFileSizeAcceptable(File file, int maxSizeKB) async {
    final sizeBytes = await file.length();
    final sizeKB = sizeBytes / 1024;
    return sizeKB <= maxSizeKB;
  }

  /// Gets the file size in KB
  static Future<double> getFileSizeKB(File file) async {
    final sizeBytes = await file.length();
    return sizeBytes / 1024;
  }

  /// Checks if an image file is too large for processing
  static Future<bool> isImageTooLargeForProcessing(File file, {int maxSizeMB = 10}) async {
    final sizeBytes = await file.length();
    final sizeMB = sizeBytes / (1024 * 1024);
    return sizeMB > maxSizeMB;
  }
}
