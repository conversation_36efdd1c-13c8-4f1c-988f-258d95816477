import 'package:country_pickers/country.dart';
import 'package:country_pickers/country_pickers.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import '../../app/theme/colors.dart';
import '../utils/device_utils.dart';
import '../utils/validator.dart';
import 'custom_input_field.dart';
import 'custom_text.dart';

class ContactTextField extends StatefulWidget {
  final TextEditingController controller;
  final Country selectedCountry;
  final void Function(Country) onCountryChanged;
  final String? Function(String?)? validator;
  final bool isUpdateMode;
  final VoidCallback? onClear;
  final double? height;
  final double? borderRadius;
  final double? fontSize;

  const ContactTextField({
    super.key,
    required this.controller,
    required this.selectedCountry,
    required this.onCountryChanged,
    this.validator,
    this.isUpdateMode = false,
    this.onClear,
    this.height,
    this.borderRadius = 12.0,
    this.fontSize = 14.0,
  });

  @override
  _ContactTextFieldState createState() => _ContactTextFieldState();
}

class _ContactTextFieldState extends State<ContactTextField> {
  void _openCountryPickerDialog() {
    final isTablet = getDeviceType(context).name == 'mobile' ||
        getDeviceType(context).name == 'tablet' ||
        getDeviceType(context).name == 'ipad';
    if (isTablet) {
      showDialog(
        context: context,
        builder: (context) => Dialog(
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          child: CountryPickerDialog(
            searchCursorColor: AppColors.primaryColor,
            isSearchable: true,
            title: CustomText(
              text: 'Select your phone code',
              size: 16,
              weight: FontWeight.w500,
              color: AppColors.blackColor,
            ),
            onValuePicked: (Country country) =>
                widget.onCountryChanged(country),
            priorityList: [
              CountryPickerUtils.getCountryByIsoCode('US'),
              CountryPickerUtils.getCountryByIsoCode('CA'),
              CountryPickerUtils.getCountryByIsoCode('GB'),
              CountryPickerUtils.getCountryByIsoCode('AU'),
              CountryPickerUtils.getCountryByIsoCode('IN'),
            ],
            itemBuilder: _buildDialogItem,
          ),
        ),
      );
    } else {
      showDialog(
        context: context,
        builder: (context) => Dialog(
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          child: SizedBox(
            width: MediaQuery.of(context).size.width * (10),
            height: MediaQuery.of(context).size.height * (0.4),
            child: Theme(
              data: Theme.of(context).copyWith(
                primaryColor: AppColors.primaryColor,
                textTheme: Theme.of(context).textTheme.copyWith(
                      bodyLarge: const TextStyle(color: Colors.black),
                      bodyMedium: const TextStyle(color: Colors.black),
                      bodySmall: const TextStyle(color: Colors.black),
                      titleMedium: const TextStyle(color: Colors.black),
                      titleSmall: const TextStyle(color: Colors.black),
                    ),
              ),
              child: CountryPickerDialog(
                // Must be true to show the search bar
                isSearchable: true,
                // Styles the text the user types
                // style : const TextStyle(
                //   color: Colors.black, // Makes the typed text black
                //   fontSize: 16.0,
                // ),

                // Sets the color of the cursor in the search field
                searchCursorColor: Colors.black,
                // Styles the entire search input field
                searchInputDecoration: InputDecoration(
                  hintText: 'Search for a country...',
                  hintStyle: const TextStyle(color: Colors.grey),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8.0),
                    borderSide: const BorderSide(color: Colors.blue),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8.0),
                    borderSide: const BorderSide(color: Colors.blue, width: 2.0),
                  ),
                  contentPadding: const EdgeInsets.symmetric(horizontal: 16.0),
                  prefixIcon: const Icon(Icons.search, color: Colors.blue),
                ),

                // Custom widget to display when no results are found
                searchEmptyView: const Center(
                  child: Text(
                    'Oops! No results found.',
                    style: TextStyle(color: Colors.red, fontSize: 18.0),
                  ),
                ),

                // ... other CountryPickerDialog properties

                // searchCursorColor: AppColors.blackTextColor,
                // searchInputDecoration: const InputDecoration(
                //   hintText: 'Search...',
                //   hintStyle: TextStyle(color: Colors.black),
                //
                // ),
                //
                // isSearchable: true,
                title: CustomText(
                  text: 'Select your phone code',
                  size: 16,
                  weight: FontWeight.w500,
                  color: AppColors.primaryLightTextColor,
                ),
                onValuePicked: (Country country) =>
                    widget.onCountryChanged(country),
                priorityList: [
                  CountryPickerUtils.getCountryByIsoCode('US'),
                  CountryPickerUtils.getCountryByIsoCode('CA'),
                  CountryPickerUtils.getCountryByIsoCode('GB'),
                  CountryPickerUtils.getCountryByIsoCode('AU'),
                  CountryPickerUtils.getCountryByIsoCode('IN'),
                ],
                itemBuilder: _buildDialogItem,
              ),
            ),
          ),
        ),
      );
    }
  }

  Widget _buildDialogItem(Country country) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
      child: Row(
        children: <Widget>[
          CountryPickerUtils.getDefaultFlagImage(country),
          const SizedBox(width: 8.0),
          CustomText(
            text: "+${country.phoneCode}",
            size: widget.fontSize,
            weight: FontWeight.w500,
            color: AppColors.primaryLightTextColor,
          ),
          const SizedBox(width: 8.0),
          Flexible(
            child: CustomText(
              text: country.name,
              size: widget.fontSize,
              weight: FontWeight.w500,
              color: AppColors.primaryLightTextColor,
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isTablet = DeviceUtils().isTabletOrIpad(context);
    final containerHeight = widget.height ?? (isTablet ? 48 : 40);
    final containerBorderRadius = widget.borderRadius ?? 12.0;
    final containerFontSize = widget.fontSize ?? 14.0;

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      // Align items to top for validation
      children: [
        MouseRegion(
          cursor: SystemMouseCursors.click,
          child: GestureDetector(
            onTap: _openCountryPickerDialog,
            child: Container(
              height: containerHeight, // Use custom height or default
              // width: isTablet ? 120 : 100,
              padding: EdgeInsets.symmetric(horizontal: 8),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(containerBorderRadius),
                border: Border.all(color: Colors.grey[300]!, width: 2),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withValues(alpha: 0.15),
                    spreadRadius: 2,
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  CountryPickerUtils.getDefaultFlagImage(
                      widget.selectedCountry),
                  SizedBox(width: 4),
                  Text(
                    '+${widget.selectedCountry.phoneCode}',
                    style: TextStyle(
                      fontSize: containerFontSize,
                      color: Colors.black,
                    ),
                  ),
                  SizedBox(width: 6),
                  SvgPicture.asset(
                    AssetsManager.droparrow,
                    height: 16,
                    width: 16,
                  ),
                ],
              ),
            ),
          ),
        ),
        SizedBox(width: 8),
        Expanded(
          child: SizedBox(
            height: containerHeight,
            child: CustomInputField(
              hintText: 'Phone',
              controller: widget.controller,
              validator: widget.validator ?? Validator.validatePhoneNumber,
              isUpdateMode: widget.isUpdateMode,
              keyboardType: TextInputType.phone,
              inputFormatters: [FilteringTextInputFormatter.digitsOnly],
              maxLength: 15,
              borderRadius: containerBorderRadius,
              fontSize: containerFontSize,
              onClear: widget.onClear,
            ),
          ),
        ),
      ],
    );
  }
}
