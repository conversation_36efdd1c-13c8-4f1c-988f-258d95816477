import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mastercookai/app/imports/core_imports.dart';

import '../../app/imports/packages_imports.dart';

class CustomSearchBar extends StatelessWidget {
  final VoidCallback? onTap; // Handle tap on search bar
  final double? height;
  final double? width;
  final TextEditingController controller;
  final String? hintText;
  final bool autoFocus;
  final int maxLines;
  final TextInputType keyboardType;
  final Function(String)? onChanged;
  final List<TextInputFormatter> formats;
  final VoidCallback? onClear; // New callback for clear action

  const CustomSearchBar({
    super.key,
    this.onTap,
    this.height,
    this.width,
    required this.controller,
    this.hintText,
    this.maxLines = 1,
    this.autoFocus = false,
    this.keyboardType = TextInputType.text,
    this.onChanged,
    this.formats = const [],
    this.onClear, // Added onClear parameter
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: GestureDetector(
        onTap: onTap,
        child:
        Container(
          width: width ?? 500.w,
          height: height ?? 50.h,
          margin: EdgeInsets.only(left: 20.w, right: 20.w),
          decoration: BoxDecoration(
            color: AppColors.lightestGreyColor.withValues(alpha: .2),
            borderRadius: BorderRadius.circular(10),
          ),
          child: TextField(
            maxLines: maxLines,
            controller: controller,
            keyboardType: keyboardType,
            autofocus: autoFocus,
            inputFormatters: formats,
            textAlignVertical: TextAlignVertical.center,

            style: context.theme.textTheme.labelMedium!.copyWith(
              fontWeight: FontWeight.w400,
              fontSize: headingSixFontSize,
            ),
            decoration: InputDecoration(
              isDense: true, // Add this line
              contentPadding: EdgeInsets.zero, // Adjust this
             // contentPadding: EdgeInsets.symmetric(vertical: 15.h),
              filled: context.theme.inputDecorationTheme.filled,
              fillColor: AppColors.lightestGreyColor.withOpacity(.6),
              hintText: hintText ?? "Search",
              hintStyle: context.theme.inputDecorationTheme.hintStyle!.copyWith(
                color: Colors.black.withAlpha(70),
                fontSize: headingSixFontSize,
                fontWeight: FontWeight.w400,
              ),
              errorStyle: context.theme.inputDecorationTheme.errorStyle,
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10),
                borderSide: const BorderSide(color: Colors.transparent),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10),
                borderSide: BorderSide(
                  color: AppColors.lightestGreyColor.withOpacity(.2),
                ),
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10),
                borderSide: const BorderSide(color: Colors.transparent),
              ),
              prefixIcon: Icon(
                Icons.search,
                color: AppColors.textGreyColor.withOpacity(.3),
              ),
              suffixIcon: controller.text.isNotEmpty
                  ? IconButton(
                icon: const Icon(
                  Icons.cancel,
                  size: 18,
                  color: Colors.grey,
                ),
                onPressed: () {
                  controller.clear();
                  if (onClear != null) {
                    onClear!(); // Call the onClear callback
                  }
                  if (onChanged != null) {
                    onChanged!(''); // Notify onChanged with empty string
                  }
                },
              )
                  : null,
            ),
            onChanged: (value) {
              if (onChanged != null) {
                onChanged!(value);
              }
              // Force rebuild to show/hide the clear icon
              (context as Element).markNeedsBuild();
            },
          ),
        ),
      ),
    );
  }
}