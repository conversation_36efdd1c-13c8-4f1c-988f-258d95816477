import 'dart:io';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:crop_image/crop_image.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/core/widgets/custom_appbar.dart';
import '../../data/models/crop_preset.dart';
import '../../utils/image_compression_utils.dart';
import 'sub_view/mobile_top_controls_widget.dart';
import 'sub_view/mobile_image_area_widget.dart';
import 'sub_view/mobile_bottom_controls_widget.dart';

class ImageCropperMobile extends StatefulWidget {
  final double? initialAspectRatio;
  final Rect initialCrop;
  final double paddingSize;
  final bool alwaysMove;
  final double minimumImageSize;
  final double maximumImageSize;
  final Function(File)? onImageCropped;
  final File pickedImage;
  final bool showCropPresets;
  final bool showGridLines;
  final bool useDelegate;
  final bool enableFreeformCrop;
  final int maxFileSizeKB;

  const ImageCropperMobile({
    super.key,
    this.initialAspectRatio,
    this.initialCrop = const Rect.fromLTRB(0.1, 0.1, 0.9, 0.9),
    this.paddingSize = 25.0,
    this.alwaysMove = true,
    this.minimumImageSize = 500,
    this.maximumImageSize = 500,
    this.onImageCropped,
    required this.pickedImage,
    this.showCropPresets = true,
    this.showGridLines = true, // Default to true for mobile
    this.useDelegate = false,
    this.enableFreeformCrop = true,
    this.maxFileSizeKB = 250,
  });

  @override
  State<ImageCropperMobile> createState() => _ImageCropperMobileState();
}

class _ImageCropperMobileState extends State<ImageCropperMobile> {
  final CropController controller = CropController();
  bool _isImageValid = false;
  String? _errorMessage;
  bool _showGridLines = true; // Always show grid lines on mobile
  double? _currentAspectRatio;

  final List<CropPreset> _cropPresets = [
    CropPreset(name: 'Free', aspectRatio: null, icon: Icons.crop_free),
    CropPreset(name: 'Square', aspectRatio: 1.0, icon: Icons.crop_square),
    CropPreset(name: '4:3', aspectRatio: 4.0 / 3.0, icon: Icons.crop_3_2),
    CropPreset(name: '16:9', aspectRatio: 16.0 / 9.0, icon: Icons.crop_16_9),
    CropPreset(name: '3:4', aspectRatio: 3.0 / 4.0, icon: Icons.crop_portrait),
    CropPreset(
        name: '9:16', aspectRatio: 9.0 / 16.0, icon: Icons.crop_portrait),
  ];

  @override
  void initState() {
    super.initState();
    _currentAspectRatio = widget.initialAspectRatio;
    _showGridLines = true; // Always show grid lines on mobile
    _validateImage();
  }

  Future<void> _validateImage() async {
    if (widget.pickedImage.path.isEmpty || !await widget.pickedImage.exists()) {
      setState(() {
        _errorMessage = 'Provided image file does not exist';
      });
      return;
    }
    try {
      final bytes = await widget.pickedImage.readAsBytes();
      final codec = await ui.instantiateImageCodec(bytes);
      final frameInfo = await codec.getNextFrame();
      final image = frameInfo.image;
      if (image.width > 0 && image.height > 0) {
        setState(() {
          _isImageValid = true;
        });
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            try {
              controller.aspectRatio = _currentAspectRatio;
              controller.crop = widget.initialCrop;
              controller.rotation = CropRotation.up;
            } catch (e) {
              print('Error setting initial controller values: $e');
              Future.delayed(const Duration(milliseconds: 100), () {
                if (mounted) {
                  try {
                    controller.aspectRatio = _currentAspectRatio;
                    controller.crop = widget.initialCrop;
                    controller.rotation = CropRotation.up;
                  } catch (e) {
                    print('Fallback error setting controller values: $e');
                    controller.aspectRatio = null;
                    controller.crop = const Rect.fromLTRB(0.1, 0.1, 0.9, 0.9);
                    controller.rotation = CropRotation.up;
                  }
                }
              });
            }
          }
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Invalid image data: $e';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: _buildMobileAppBar(),
      body: Column(
        children: [
          Expanded(
            child: MobileImageAreaWidget(
              pickedImage: widget.pickedImage,
              controller: controller,
              isImageValid: _isImageValid,
              errorMessage: _errorMessage,
              showGridLines: _showGridLines,
              paddingSize: widget.paddingSize,
              alwaysMove: widget.alwaysMove,
              minimumImageSize: widget.minimumImageSize,
              maximumImageSize: widget.maximumImageSize,
              onCrop: (rect) {
                controller.crop = rect;
              },
            ),
          ),
          MobileTopControlsWidget(
            onZoomIn: _zoomIn,
            onZoomOut: _zoomOut,
            onRotateLeft: _rotateLeft,
            onRotateRight: _rotateRight,
            onReset: _resetCrop,
          ),
          MobileBottomControlsWidget(
            cropPresets: _cropPresets,
            currentAspectRatio: _currentAspectRatio,
            onPresetSelected: _selectCropPreset,
            onCropImage: _finished,
            onKeepOriginal: _keepOriginal,
          ),
        ],
      ),
    );
  }

  PreferredSizeWidget _buildMobileAppBar() {
    return CustomAppBar(
      title: 'Crop Images',
      onPressed: () => Navigator.pop(context),
      actions: [],
    );
  }

  void _selectCropPreset(CropPreset preset) {
    setState(() {
      _currentAspectRatio = preset.aspectRatio;
    });
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted && _isImageValid) {
        try {
          controller.aspectRatio = preset.aspectRatio;
          controller.crop = widget.initialCrop;
        } catch (e) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error applying crop preset: $e')),
          );
        }
      }
    });
  }

  // Zoom and rotate methods
  void _zoomIn() {
    if (!_isImageValid) return;
    try {
      final currentCrop = controller.crop;
      const zoomFactor = 0.9;
      final centerX = (currentCrop.left + currentCrop.right) / 2;
      final centerY = (currentCrop.top + currentCrop.bottom) / 2;
      final width = (currentCrop.right - currentCrop.left) * zoomFactor;
      final height = (currentCrop.bottom - currentCrop.top) * zoomFactor;

      final newLeft = (centerX - width / 2).clamp(0.0, 1.0);
      final newTop = (centerY - height / 2).clamp(0.0, 1.0);
      final newRight = (centerX + width / 2).clamp(0.0, 1.0);
      final newBottom = (centerY + height / 2).clamp(0.0, 1.0);

      controller.crop = Rect.fromLTRB(newLeft, newTop, newRight, newBottom);
    } catch (e) {
      print('Zoom in error: $e');
    }
  }

  void _zoomOut() {
    if (!_isImageValid) return;
    try {
      final currentCrop = controller.crop;
      const zoomFactor = 1.1;
      final centerX = (currentCrop.left + currentCrop.right) / 2;
      final centerY = (currentCrop.top + currentCrop.bottom) / 2;
      final width = (currentCrop.right - currentCrop.left) * zoomFactor;
      final height = (currentCrop.bottom - currentCrop.top) * zoomFactor;

      final newLeft = (centerX - width / 2).clamp(0.0, 1.0);
      final newTop = (centerY - height / 2).clamp(0.0, 1.0);
      final newRight = (centerX + width / 2).clamp(0.0, 1.0);
      final newBottom = (centerY + height / 2).clamp(0.0, 1.0);

      controller.crop = Rect.fromLTRB(newLeft, newTop, newRight, newBottom);
    } catch (e) {
      print('Zoom out error: $e');
    }
  }


  void _rotateLeft() {
    if (!_isImageValid) return;
    try {
      final currentRotation = controller.rotation;
      switch (currentRotation) {
        case CropRotation.up:
          controller.rotation = CropRotation.left;
          break;
        case CropRotation.left:
          controller.rotation = CropRotation.down;
          break;
        case CropRotation.down:
          controller.rotation = CropRotation.right;
          break;
        case CropRotation.right:
          controller.rotation = CropRotation.up;
          break;
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error rotating left: $e')),
      );
    }
  }

  void _rotateRight() {
    if (!_isImageValid) return;
    try {
      final currentRotation = controller.rotation;
      switch (currentRotation) {
        case CropRotation.up:
          controller.rotation = CropRotation.right;
          break;
        case CropRotation.right:
          controller.rotation = CropRotation.down;
          break;
        case CropRotation.down:
          controller.rotation = CropRotation.left;
          break;
        case CropRotation.left:
          controller.rotation = CropRotation.up;
          break;
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error rotating right: $e')),
      );
    }
  }

  void _resetCrop() {
    if (!_isImageValid) return;
    try {
      controller.rotation = CropRotation.up;
      controller.crop = widget.initialCrop;
      controller.aspectRatio = _currentAspectRatio;
      setState(() {
        _showGridLines = true; // Reset grid lines
      });
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error resetting crop: $e')),
      );
    }
  }

  void _keepOriginal() {
    try {
      if (widget.useDelegate) {
        // If using delegate, call the callback with original image
        widget.onImageCropped?.call(widget.pickedImage);
        Navigator.pop(context);
      } else {
        // If not using delegate, return the original image file
        Navigator.pop(context, widget.pickedImage);
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error keeping original: $e')),
      );
    }
  }



  Future<void> _finished() async {
    if (!mounted || !_isImageValid) return;

    try {
      // Get the original image bytes
      final originalBytes = await widget.pickedImage.readAsBytes();
      final codec = await ui.instantiateImageCodec(originalBytes);
      final frameInfo = await codec.getNextFrame();
      final ui.Image originalImage = frameInfo.image;

      // Get the crop rectangle and rotation
      final cropRect = controller.crop;
      final rotation = controller.rotation;
      final imageWidth = originalImage.width.toDouble();
      final imageHeight = originalImage.height.toDouble();

      // Adjust crop rectangle based on rotation
      Rect pixelRect;
      double outputWidth, outputHeight;
      switch (rotation) {
        case CropRotation.up:
        case CropRotation.down:
          pixelRect = Rect.fromLTRB(
            cropRect.left * imageWidth,
            cropRect.top * imageHeight,
            cropRect.right * imageWidth,
            cropRect.bottom * imageHeight,
          );
          outputWidth = pixelRect.width;
          outputHeight = pixelRect.height;
          break;
        case CropRotation.left:
        case CropRotation.right:
          // Swap width and height for 90/270 degree rotations
          pixelRect = Rect.fromLTRB(
            cropRect.left * imageHeight,
            cropRect.top * imageWidth,
            cropRect.right * imageHeight,
            cropRect.bottom * imageWidth,
          );
          outputWidth = pixelRect.height;
          outputHeight = pixelRect.width;
          break;
      }

      // Create a new picture with the correct dimensions
      final recorder = ui.PictureRecorder();
      final canvas = Canvas(recorder);

      // Apply rotation transformation
      canvas.save();
      switch (rotation) {
        case CropRotation.up:
          // No rotation needed
          break;
        case CropRotation.right:
          canvas.translate(outputWidth, 0);
          canvas.rotate(90 * 3.141592653589793 / 180);
          break;
        case CropRotation.down:
          canvas.translate(outputWidth, outputHeight);
          canvas.rotate(180 * 3.141592653589793 / 180);
          break;
        case CropRotation.left:
          canvas.translate(0, outputHeight);
          canvas.rotate(270 * 3.141592653589793 / 180);
          break;
      }

      // Draw the cropped portion
      canvas.drawImageRect(
        originalImage,
        pixelRect,
        Rect.fromLTWH(0, 0, outputWidth, outputHeight),
        Paint(),
      );
      canvas.restore();

      final picture = recorder.endRecording();
      final croppedImage = await picture.toImage(
        outputWidth.toInt(),
        outputHeight.toInt(),
      );

      // Compress image using the new utility
      final tempFile = await ImageCompressionUtils.compressImageToSize(
        croppedImage,
        maxSizeKB: widget.maxFileSizeKB,
        customFileName: 'cropped_author_image_${DateTime.now().millisecondsSinceEpoch}.jpg',
      );

      // Log file size
      final fileSizeKB = (await tempFile.length()) / 1024;
      print('Cropped image size: ${fileSizeKB.toStringAsFixed(1)} KB');
      print('useDelegate: ${widget.useDelegate}');

      if (mounted) {
        if (widget.useDelegate) {
          widget.onImageCropped?.call(tempFile);
          Navigator.pop(context, true);
        } else {
          Navigator.pop(context, tempFile);
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error cropping image: $e')),
        );
      }
    }
  }
}
