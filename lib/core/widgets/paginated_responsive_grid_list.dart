import 'package:flutter/material.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';
import 'package:responsive_grid/responsive_grid.dart';
import '../data/models/view_type.dart';

class PaginatedResponsiveGridList<T> extends StatefulWidget {
  final List<T> items; // Generic list of items
  final Widget Function(T item, int index) itemBuilder; // Builder for each item
  final double desiredItemWidth; // Desired width of each item (for grid)
  final double minSpacing; // Minimum spacing between items (for grid)
  final int pageSize; // Number of items per page
  final VoidCallback? onLoadMore; // Callback for loading more items
  final ViewType viewType; // Toggle between grid and list view

  const PaginatedResponsiveGridList({
    super.key,
    required this.items,
    required this.itemBuilder,
    this.desiredItemWidth = 100,
    this.minSpacing = 10,
    this.pageSize = 10,
    this.onLoadMore,
    this.viewType = ViewType.grid
    , // Default to grid view
  });

  @override
  State<PaginatedResponsiveGridList<T>> createState() =>
      _PaginatedResponsiveGridState<T>();
}

class _PaginatedResponsiveGridState<T>
    extends State<PaginatedResponsiveGridList<T>> {
  late int _currentPage;
  late List<T> _displayedItems;
  bool _isLoadingMore = false; // State to show/hide loader

  @override
  void initState() {
    super.initState();
    _currentPage = 0;
    _updateDisplayedItems();
  }

  void _updateDisplayedItems() {
    final start = 0;
    final end = (_currentPage + 1) * widget.pageSize;
    _displayedItems = widget.items.sublist(
      start,
      end.clamp(0, widget.items.length),
    );
  }

  Future<void> _loadNextPage() async {
    if (_currentPage * widget.pageSize < widget.items.length &&
        !_isLoadingMore &&
        widget.onLoadMore != null) {
      setState(() {
        _isLoadingMore = true;
      });
      try {
        await Future.delayed(const Duration(seconds: 1)); // Simulate API call
        widget.onLoadMore!();
        if (mounted) {
          setState(() {
            _currentPage++;
            _updateDisplayedItems();
            _isLoadingMore = false;
          });
        }
      } catch (e) {
        if (mounted) {
          setState(() {
            _isLoadingMore = false;
          });
        }
        debugPrint('Error loading more items: $e');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Expanded(
          child: NotificationListener<ScrollNotification>(
            onNotification: (scrollNotification) {
              if (scrollNotification is ScrollEndNotification &&
                  scrollNotification.metrics.pixels ==
                      scrollNotification.metrics.maxScrollExtent &&
                  !_isLoadingMore) {
                _loadNextPage();
              }
              return false;
            },
            child: widget.viewType == ViewType.grid
                ? ResponsiveGridList(
                    shrinkWrap: true,
                    desiredItemWidth: widget.desiredItemWidth,
                    minSpacing: widget.minSpacing,
                    children: _displayedItems.asMap().entries.map((entry) {
                      final index = entry.key;
                      final item = entry.value;
                      return widget.itemBuilder(item, index);
                    }).toList(),
                  )
                : ListView.builder(
                    itemCount: _displayedItems.length,
                    itemBuilder: (context, index) {
                      final item = _displayedItems[index];
                      return Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16,vertical: 4.0),
                        child: widget.itemBuilder(item, index),
                      );
                    },
                  ),
          ),
        ),
        if (_isLoadingMore)
          LoadingAnimationWidget.fallingDot(
            color: Colors.grey,
            size: 50.0,
          )
      ],
    );
  }
}
