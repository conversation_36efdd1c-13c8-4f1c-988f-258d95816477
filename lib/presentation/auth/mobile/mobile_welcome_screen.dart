import 'package:flutter/foundation.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:flutter_svg/svg.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/presentation/auth/mobile/mobile_login_screen.dart';
import '../../../app/imports/packages_imports.dart';
import 'dart:io' show Platform;
import '../../../core/utils/device_utils.dart';
import '../../../core/widgets/YouTubeLauncher.dart';
import '../../../core/widgets/YouTubePlayer.dart';
import '../../../core/widgets/custom_button.dart';

class MobileWelcomeScreen extends HookConsumerWidget {
  const MobileWelcomeScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final webViewController = useState<InAppWebViewController?>(null);
    return Scaffold(
      resizeToAvoidBottomInset: false,
      body: Stack(
        children: [
          Container(
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.95),
              borderRadius: BorderRadius.circular(30.r),
            ),
            child: Row(
              children: [
                // Left Section
                Expanded(
                  child: Container(
                    padding: EdgeInsets.all(16.w),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(30.r),
                        bottomLeft: Radius.circular(30.r),
                      ),
                      image: DecorationImage(
                        image: AssetImage(AssetsManager.recipe_bg_img),
                        fit: BoxFit.cover,
                      ),
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(
                              top: 80, left: 20, right: 20),
                          child: Image.asset(
                            AssetsManager.mastercook_ai_logo,
                            height: 180,
                            width: 300,
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.only(top: 80),
                          child: Align(
                            alignment: Alignment.bottomCenter,
                            child: Text(
                              "Watch getting started video",
                              style: TextStyle(
                                fontFamily: 'Inter',
                                fontSize: 14,
                                fontWeight: FontWeight.w400,
                                letterSpacing: 0,
                                color: AppColors.primaryGreyColor,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ),
                        SizedBox(height: 10.h),
                        Padding(
                          padding: const EdgeInsets.only(
                              top: 10.0, left: 20.0, right: 20.0),
                          child: Container(
                            width: 338,
                            height: 180,
                            decoration: BoxDecoration(
                              color: Color(0xFF333333),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: ClipRRect(
                                borderRadius: BorderRadius.circular(4),
                                child: (kIsWeb || Platform.isMacOS)
                                    ? InAppYouTubePlayer(
                                        videoId: "d1q4nwMUegA",
                                        onWebViewCreated: (controller) {
                                          webViewController.value = controller;
                                        },
                                      )
                                    : (Platform.isWindows || Platform.isLinux)
                                        ? YouTubeLauncher()
                                        : InAppYouTubePlayer(
                                            videoId: "d1q4nwMUegA",
                                            onWebViewCreated: (controller) {
                                              webViewController.value = controller;
                                            },
                                          )),
                          ),
                        ),
                        SizedBox(height: 100.h),
                        ElevatedButton(
                          onPressed: () {
                            if (webViewController.value != null) {
                              webViewController.value!.evaluateJavascript(
                                  source:
                                      "var video = document.getElementsByTagName('video')[0]; if (video) { video.pause(); }");
                            }
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (context) =>
                                      const MobileLoginScreen()),
                            );
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.primaryColor,
                            // Button background color
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(
                                  10.0), // Rounded corners
                            ),
                            padding: const EdgeInsets.symmetric(
                                horizontal: 40, vertical: 10),
                            // Padding inside the button
                            elevation: 5, // Shadow
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            // Make the row take minimum space
                            children: [
                              const Text(
                                'Get Started',
                                style: TextStyle(
                                  color: Colors.white, // Text color
                                  fontSize: 14,
                                  fontFamily: "Inter",
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              const SizedBox(width: 20),
                              // Space between text and icon
                              SvgPicture.asset(
                                AssetsManager.ic_getstarted,
                                height: 24,
                                width: 24,
                              )
                            ],
                          ),
                        )
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
