import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter_svg/svg.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/core/utils/Utils.dart';
import 'package:mastercookai/core/widgets/custom_input_field.dart';
import 'package:mastercookai/core/data/models/user_types_response.dart';
import 'package:mastercookai/core/widgets/custom_text.dart';
import 'package:mastercookai/presentation/auth/login_screen.dart';
import 'package:mastercookai/presentation/profile/mobileui/sub_views/build_user_types_dopdown_field.dart';
import '../../../app/imports/packages_imports.dart';
import 'dart:io' show Platform;
import '../../../core/utils/device_utils.dart';
import '../../../core/widgets/YouTubeLauncher.dart';
import '../../../core/widgets/YouTubePlayer.dart';
import '../../../core/widgets/custom_button.dart';
import '../../core/network/app_status.dart';
import '../../core/providers/auth/controllers/auth_notifier.dart';
import '../../core/utils/validator.dart';
import 'mobile/mobile_signup_screen.dart';
import 'sub_views/otp_screen.dart';

class SignupScreen extends HookConsumerWidget {
  const SignupScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    ref.listen(authNotifierProvider, (previous, next) {
      if (next.status == AppStatus.otpVerificationSuccess) {
        ref.read(authNotifierProvider.notifier).reset();
      }
    });
    final authState = ref.watch(authNotifierProvider);
    final authNotifier = ref.read(authNotifierProvider.notifier);

    final fullNameController = useTextEditingController();
    final emailController = useTextEditingController();
    final passwordController = useTextEditingController();
    final formKey = useMemoized(() => GlobalKey<FormState>());
    final selectedUserType = useState<UserType?>(null);
    final isChecked = useState(false);


    return  getDeviceType(context).name == 'mobile'
        ? MobileSignupScreen()
        :  Scaffold(
      resizeToAvoidBottomInset: false,
      body: Builder(
        builder: (BuildContext context) {
          return Stack(
            children: [
              Positioned.fill(
                child: Image.asset(
              AssetsManager.background_img,
              fit: BoxFit.cover,
            ),
          ),
          Center(
            child: FractionallySizedBox(
              widthFactor: DeviceUtils().isTabletOrIpad(context) ? 0.8 : 0.45,
              heightFactor: 0.62,
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.95),
                  borderRadius: BorderRadius.circular(30.r),
                ),
                child: Row(
                  children: [
                    // Left Section
                    Visibility(
                      visible: getDeviceType(context).name == 'mobile'
                          ? false
                          : true,
                      child: Expanded(
                        child: Container(
                          padding: EdgeInsets.all(16.w),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(30.r),
                              bottomLeft: Radius.circular(30.r),
                            ),
                            image: DecorationImage(
                              image: AssetImage(AssetsManager.recipe_bg_img),
                              fit: BoxFit.cover,
                            ),
                          ),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Padding(
                                padding: const EdgeInsets.only(top: 50),
                                child: Image.asset(
                                  AssetsManager.mastercook_ai_logo,
                                  height: 250.h,
                                  width: 400.w,
                                ),
                              ),
                              SizedBox(height: 10.h),
                              Padding(
                                padding: const EdgeInsets.only(top: 70),
                                child: Align(
                                  alignment: Alignment.bottomCenter,
                                  child: Text(
                                    "Watch getting started video",
                                    style: TextStyle(
                                      fontFamily: 'Inter',
                                      fontSize:
                                          DeviceUtils().isTabletOrIpad(context)
                                              ? 22
                                              : responsiveFont(23).sp,
                                      fontWeight: FontWeight.w400,
                                      height: 16 / 12,
                                      letterSpacing: 0,
                                      color: Color(0xFF4F4F4F),
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsets.only(
                                    top: 10.0, left: 20.0, right: 20.0),
                                child: Container(
                                  width: 338,
                                  height: 180,
                                  decoration: BoxDecoration(
                                    color: Color(0xFF333333),
                                    borderRadius: BorderRadius.circular(4),
                                  ),
                                  child: ClipRRect(
                                      borderRadius: BorderRadius.circular(4),
                                      child: (kIsWeb || Platform.isMacOS)
                                          ? const InAppYouTubePlayer(
                                              videoId: "d1q4nwMUegA")
                                          : (Platform.isWindows ||
                                                  Platform.isLinux)
                                              ? YouTubeLauncher()
                                              : InAppYouTubePlayer(
                                                  videoId: "d1q4nwMUegA")
                                      // Center(
                                      //   child: Icon(
                                      //     Icons.play_circle_fill,
                                      //     size: 48,
                                      //     color: Colors.white.withValues(alpha: 0.8),
                                      //   ),
                                      //
                                      // ),
                                      ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                    // Right Section
                    Expanded(
                      child: authState.status == AppStatus.success
                          ? OtpScreen(
                              email: emailController.text.trim(),
                              fullName: fullNameController.text.trim(),
                              password: passwordController.text.trim(),
                              userType: selectedUserType.value!.id.toString(),
                            )
                          : Padding(
                              padding: EdgeInsets.all(18.w),
                              child: Form(
                                key: formKey,
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    SizedBox(height: 20.h),
                                    RichText(
                                      textAlign: TextAlign.center,
                                      text: TextSpan(
                                        style: TextStyle(
                                          fontFamily: 'Inter',
                                          fontSize: responsiveFont(18).sp,
                                          height: 16 / 12,
                                          letterSpacing: 0,
                                        ),
                                        children: [
                                          TextSpan(
                                            text: "Already have an Account? ",
                                            style: TextStyle(
                                              fontSize: 12,
                                              color: Color(0xFF4F4F4F),
                                              fontWeight: FontWeight.w400,
                                            ),
                                          ),
                                          TextSpan(
                                            text: "Login",
                                            style: TextStyle(
                                              fontSize: 12,
                                              color: Color(0xFF007AFF),
                                              fontWeight: FontWeight.w400,
                                              decoration:
                                                  TextDecoration.underline,
                                            ),
                                            recognizer: TapGestureRecognizer()
                                              ..onTap = () {
                                                Navigator.push(
                                                  context,
                                                  MaterialPageRoute(
                                                      builder: (context) =>
                                                          const LoginScreen()),
                                                );
                                              },
                                          ),
                                        ],
                                      ),
                                    ),
                                    SizedBox(height: 50),

                                    CustomText(
                                      text: "Sign Up",
                                      color: AppColors.primaryGreyColor,
                                      size:
                                          DeviceUtils().isTabletOrIpad(context)
                                              ? 24
                                              : responsiveFont(40.sp),
                                      weight: FontWeight.w700,
                                      fontFamily: 'Inter',
                                    ),

                                    SizedBox(height: 50),
                                    Container(
                                      margin: EdgeInsets.symmetric(
                                          horizontal: 50.w),
                                      child: Column(
                                        children: [
                                          CustomInputField(
                                            hintText: 'Full Name',
                                            // fontSize: DeviceUtils().isTabletOrIpad(context) ? 20 : responsiveFont(20).sp,
                                            controller: fullNameController,
                                            keyboardType: TextInputType.text,
                                            //autoFocus: true,
                                            validator:
                                                Validator.validateFullName,
                                          ),
                                          SizedBox(height: 20.h),
                                          CustomInputField(
                                            hintText: 'Email',
                                            // fontSize: DeviceUtils().isTabletOrIpad(context) ? 20 : responsiveFont(20).sp,
                                            controller: emailController,
                                            keyboardType:
                                                TextInputType.emailAddress,
                                            //autoFocus: true,
                                            validator: Validator.validateEmail,
                                          ),
                                          SizedBox(height: 20.h),
                                          CustomInputField(
                                            hintText: 'Password',
                                            controller: passwordController,
                                            keyboardType:
                                                TextInputType.emailAddress,
                                            isPassword: true,
                                            validator:
                                                Validator.validatePassword,
                                          ),
                                          SizedBox(height: 20.h),
                                          UserTypesDropdownField(
                                            onChanged: (userType) {
                                              selectedUserType.value =
                                                  userType;
                                            },
                                          ),
                                          SizedBox(height: 10.h),
                                          Row(
                                            children: [
                                              Checkbox(
                                                value: isChecked.value,
                                                onChanged: (bool? newValue) {
                                                  isChecked.value =
                                                      newValue ?? false;
                                                },
                                                activeColor: Colors.red,
                                                side: const BorderSide(
                                                    color: Colors.red,
                                                    width: 1),
                                                shape: RoundedRectangleBorder(
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            2)),
                                                checkColor: Colors.white,
                                              ),
                                              RichText(
                                                textAlign: TextAlign.center,
                                                text: TextSpan(
                                                  style: TextStyle(
                                                    fontFamily: 'Inter',
                                                    fontSize: 14,
                                                    letterSpacing: 0,
                                                  ),
                                                  children: [
                                                    TextSpan(
                                                      text: "I accept the ",
                                                      style: TextStyle(
                                                        fontSize: 12,
                                                        color:
                                                            Color(0xFF4F4F4F),
                                                        fontWeight:
                                                            FontWeight.w400,
                                                      ),
                                                    ),
                                                    TextSpan(
                                                      text:
                                                          " Privacy Policy and Terms of Use.",
                                                      style: TextStyle(
                                                        fontSize: 12,
                                                        color:
                                                            Color(0xFF007AFF),
                                                        fontWeight:
                                                            FontWeight.w400,
                                                        decoration:
                                                            TextDecoration
                                                                .underline,
                                                      ),
                                                      recognizer:
                                                          TapGestureRecognizer()
                                                            ..onTap = () {
                                                              Utils().launchURL(
                                                                   url: 'http://www.mastercook.com/privacy-policy');
                                                            },
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ],
                                          ),
                                          SizedBox(height: 50),
                                          CustomButton(
                                            text: 'Sign Up',
                                            height: 30,
                                            isLoading: authState.status ==
                                                AppStatus.loading,
                                            width: 250,
                                            onPressed: () {
                                              if (formKey.currentState!
                                                  .validate()) {
                                                if (selectedUserType.value ==
                                                    null) {
                                                  Utils().showFlushbar(context,
                                                      message:
                                                          'Please select a user type.',
                                                      isError: true);
                                                  return;
                                                }
                                                if (!isChecked.value) {
                                                  Utils().showFlushbar(context,
                                                      message:
                                                          'Please accept the privacy policy and terms of use.',
                                                      isError: true);
                                                  return;
                                                }
                                                authNotifier.register(
                                                  context,
                                                  fullNameController.text
                                                      .trim(),
                                                  passwordController.text
                                                      .trim(),
                                                  emailController.text.trim(),
                                                  selectedUserType.value!.id
                                                      .toString(),
                                                );
                                              }
                                            },
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      );
        },
      ),
    );
  }
}
