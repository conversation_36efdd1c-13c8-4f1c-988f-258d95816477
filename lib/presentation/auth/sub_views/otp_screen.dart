// import 'package:flutter/material.dart';
// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:go_router/go_router.dart';
// import 'package:mastercookai/app/imports/packages_imports.dart';
// import 'package:mastercookai/core/providers/auth/controllers/auth_notifier.dart';

// import '../../../app/theme/colors.dart';
// import '../../../core/network/app_status.dart';
// import '../../../core/widgets/custom_button.dart';
// import '../../../core/widgets/custom_text.dart';

// class OtpScreen extends HookConsumerWidget {
//   final String? email;
//   const OtpScreen({super.key, this.email});

//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     final authState = ref.watch(authNotifierProvider);
//     final authNotifier = ref.read(authNotifierProvider.notifier);
//     final fieldOne = useTextEditingController();
//     final fieldTwo = useTextEditingController();
//     final fieldThree = useTextEditingController();
//     final fieldFour = useTextEditingController();
//     final fieldFive = useTextEditingController();
//     final fieldSix = useTextEditingController();

//     void onComplete() {
//       String otp = fieldOne.text + fieldTwo.text + fieldThree.text + fieldFour.text + fieldFive.text + fieldSix.text;
//       if (otp.length == 6) {
//         authNotifier.verifyotp(context, email!, otp);
//       }
//     }
//     return Scaffold(
//       body: Column(
//         mainAxisAlignment: MainAxisAlignment.center,
//         children: [
//           CustomText(
//             text: "OTP Verification",
//             color: AppColors.blackTextColor,
//             size: 24,
//             weight: FontWeight.w700,
//             fontFamily: 'Inter',
//           ),
//           SizedBox(height: 50),
//           CustomText(
//             text: "Please enter the 4-digit code sent to your email",
//             color: AppColors.texGreyColor,
//             size: 14,
//             weight: FontWeight.w400,
//             fontFamily: 'Inter',
//           ),
//           const SizedBox(height: 30),
//           Padding(
//             padding: const EdgeInsets.symmetric(horizontal: 10),
//             child: Row(
//               mainAxisAlignment: MainAxisAlignment.spaceEvenly,
//               children: [
//                 _otpField(context, fieldOne),
//                 SizedBox(width: 5),
//                 _otpField(context, fieldTwo),
//                 SizedBox(width: 5),
//                 _otpField(context, fieldThree),
//                 SizedBox(width: 5),
//                 _otpField(context, fieldFour),
//                 SizedBox(width: 5),
//                 _otpField(context, fieldFive),
//                 SizedBox(width: 5),
//                 _otpField(context, fieldSix),
//               ],
//             ),
//           ),
//           const SizedBox(height: 30),
//           CustomButton(
//             text: 'Verify',
//             height: 30,
//             weight: FontWeight.w500,
//             width: 250,
//             isLoading: authState.status == AppStatus.loading,
//             borderRadius: 10,
//             onPressed: () {
//               onComplete();
//             },
//           ),

//         ],
//       ),
//     );
//   }

//   Widget _otpField(BuildContext context, TextEditingController controller) {
//     return SizedBox(
//         width: 50,
//         height: 100,
//         child: TextField(
//           controller: controller,
//           autofocus: true,
//           onChanged: (value) {
//             if (value.length == 1) {
//               FocusScope.of(context).nextFocus();
//             }
//           },
//           keyboardType: TextInputType.number,
//           textAlign: TextAlign.center,
//           cursorColor: AppColors.blackTextColor,
//           maxLength: 1,
//           style: TextStyle(
//             color: AppColors.primaryLightTextColor,
//             fontSize: 14,
//             fontFamily: "Inter-Regular",
//             fontWeight: FontWeight.w400,
//           ),
//           decoration: InputDecoration(
//             hintStyle: TextStyle(
//               color: AppColors.primaryLightTextColor,
//               fontSize: 15,
//               fontFamily: "Inter-Regular",
//               fontWeight: FontWeight.w400,
//             ),
//             contentPadding: EdgeInsets.symmetric(
//               vertical: 1 > 1 ? 15 : 15, // Adjust padding for multi-line
//               horizontal: 12,
//             ),
//             border: OutlineInputBorder(
//               borderRadius: BorderRadius.circular(10),
//               borderSide: const BorderSide(
//                 color: AppColors.primaryBorderColor,
//                 width: 2,
//               ),
//             ),
//             enabledBorder: OutlineInputBorder(
//               borderRadius: BorderRadius.circular(10),
//               borderSide: BorderSide(
//                 color: Colors.grey[300]!,
//                 width: 2,
//               ),
//             ),
//             focusedBorder: OutlineInputBorder(
//               borderRadius: BorderRadius.circular(10),
//               borderSide: const BorderSide(
//                 color: AppColors.primaryBorderColor,
//                 width: 2,
//               ),
//             ),
//             isDense: true,
//           ),
//         ));
//   }
// }

import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:mastercookai/app/imports/packages_imports.dart';
import 'package:mastercookai/core/providers/auth/controllers/auth_notifier.dart';

import '../../../app/theme/colors.dart';
import '../../../core/network/app_status.dart';
import '../../../core/utils/device_utils.dart';
import '../../../core/widgets/custom_button.dart';
import '../../../core/widgets/custom_text.dart';

class OtpScreen extends HookConsumerWidget {
  final String? email;
  final String? fullName;
  final String? password;
  final String? userType;
  const OtpScreen(
      {super.key, this.email, this.fullName, this.password, this.userType});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authState = ref.watch(authNotifierProvider);
    final authNotifier = ref.read(authNotifierProvider.notifier);
    final fieldOne = useTextEditingController();
    final fieldTwo = useTextEditingController();
    final fieldThree = useTextEditingController();
    final fieldFour = useTextEditingController();
    final fieldFive = useTextEditingController();
    final fieldSix = useTextEditingController();
    final remainingTime = useState(60); // Timer state
    final timerActive = useState(true); // Timer active state

    // Timer logic
    useEffect(() {
      if (timerActive.value) {
        final timer = Timer.periodic(const Duration(seconds: 1), (timer) {
          if (remainingTime.value > 0) {
            remainingTime.value--;
          } else {
            timerActive.value = false;
            timer.cancel();
          }
        });
        return () => timer.cancel();
      }
      return null;
    }, [timerActive.value]);

    void onComplete() {
      String otp = fieldOne.text + fieldTwo.text + fieldThree.text + fieldFour.text + fieldFive.text + fieldSix.text;
      if (otp.length == 6) {
        authNotifier.verifyotp(context, email!, otp);
      }
    }

    void resendOtp() {
      // Reset timer and resend OTP
      remainingTime.value = 60;
      timerActive.value = true;
      authNotifier.register(
        context,
        fullName!,
        password!,
        email!,
        userType!,
        isResend: true,
      );
    }

    return Scaffold(
      body: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CustomText(
            text: "OTP Verification",
            color: AppColors.blackTextColor,
            size: 24,
            weight: FontWeight.w700,
            fontFamily: 'Inter',
          ),
          const SizedBox(height: 50),
          CustomText(
            text: "OTP sent successfully on $email",
            color: AppColors.texGreyColor,
            size: 14,
            weight: FontWeight.w400,
            fontFamily: 'Inter',
          ),
          const SizedBox(height: 8),
          CustomText(
            text: "Please enter the 6-digit code sent to your email",
            color: AppColors.texGreyColor,
            size: 14,
            weight: FontWeight.w400,
            fontFamily: 'Inter',
          ),
          const SizedBox(height: 30),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                _otpField(context, fieldOne),
                const SizedBox(width: 8),
                _otpField(context, fieldTwo),
                const SizedBox(width: 8),
                _otpField(context, fieldThree),
                const SizedBox(width: 8),
                _otpField(context, fieldFour),
                const SizedBox(width: 8),
                _otpField(context, fieldFive),
                const SizedBox(width: 8),
                _otpField(context, fieldSix),
              ],
            ),
          ),
          const SizedBox(height: 20),
          GestureDetector(
            onTap: timerActive.value ? null : resendOtp,
            child: Text(
              timerActive.value 
                  ? "Resend OTP in ${remainingTime.value}s" 
                  : "Resend OTP",
              style: TextStyle(
                color: timerActive.value 
                    ? Colors.grey 
                    : Colors.blue,
                fontSize: 14,
                fontWeight: FontWeight.w400,
                fontFamily: 'Inter',
              ),
            ),
          ),
          const SizedBox(height: 20),
          CustomButton(
            text: 'Verify',
            height: 30,
            weight: FontWeight.w500,
            width: 250,
            isLoading: authState.status == AppStatus.creating,
            borderRadius: 10,
            onPressed: () {
              onComplete();
            },
          ),
          const SizedBox(height: 20),
          TextButton(
            onPressed: () {
              authNotifier.reset();
              context.pop(); // Navigate back to the sign-up screen
            },
            child: const Text(
              'Back to Sign Up',
              style: TextStyle(
                color: Colors.blue,
                fontSize: 14,
                fontWeight: FontWeight.w400,
                fontFamily: 'Inter',
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Widget _otpField(BuildContext context, TextEditingController controller) {
  //   return SizedBox(
  //       width: 50,
  //       height: 100,
  //       child: TextField(
  //         controller: controller,
  //         autofocus: true,
  //         onChanged: (value) {
  //           if (value.length == 1) {
  //             FocusScope.of(context).nextFocus();
  //           }
  //         },
  //         keyboardType: TextInputType.number,
  //         textAlign: TextAlign.center,
  //         cursorColor: AppColors.blackTextColor,
  //         maxLength: 1,
  //         style: TextStyle(
  //           color: AppColors.primaryLightTextColor,
  //           fontSize: 14,
  //           fontFamily: "Inter-Regular",
  //           fontWeight: FontWeight.w400,
  //         ),
  //         decoration: InputDecoration(
  //           hintStyle: TextStyle(
  //             color: AppColors.primaryLightTextColor,
  //             fontSize: 15,
  //             fontFamily: "Inter-Regular",
  //             fontWeight: FontWeight.w400,
  //           ),
  //           contentPadding: EdgeInsets.symmetric(
  //             vertical: 1 > 1 ? 15 : 15, // Adjust padding for multi-line
  //             horizontal: 12,
  //           ),
  //           border: OutlineInputBorder(
  //             borderRadius: BorderRadius.circular(10),
  //             borderSide: const BorderSide(
  //               color: AppColors.primaryBorderColor,
  //               width: 2,
  //             ),
  //           ),
  //           enabledBorder: OutlineInputBorder(
  //             borderRadius: BorderRadius.circular(10),
  //             borderSide: BorderSide(
  //               color: Colors.grey[300]!,
  //               width: 2,     
  //             ),
  //           ),
  //           focusedBorder: OutlineInputBorder(
  //             borderRadius: BorderRadius.circular(10),
  //             borderSide: const BorderSide(
  //               color: AppColors.primaryBorderColor,
  //               width: 2,
  //             ),
  //           ),
  //           isDense: true,
  //         ),
  //       ));
  // }

  Widget _otpField(BuildContext context, TextEditingController controller) {
    final screenSize = MediaQuery.of(context).size;
    final screenWidth = screenSize.width;
    final isTablet = DeviceUtils().isTabletOrIpad(context);
    final isDesktop = DeviceUtils.isDesktop(context);

    // Calculate available width for OTP fields (accounting for padding and margins)
    final availableWidth = screenWidth - 40; // Account for screen padding (20px each side)
    final totalMargins = 2 * 8; // 5 gaps of 8px between 6 fields
    final fieldAvailableWidth = (availableWidth - totalMargins) / 6;

    // Calculate responsive field size based on available space and device type
    double fieldSize;
    double fontSize;
    double borderRadius;
    double verticalPadding;

    if (isDesktop) {
      // Desktop/Web - larger screens, use percentage of available width
      fieldSize = (fieldAvailableWidth * 0.4).clamp(20.0, 40.0);
      fontSize = (fieldSize * 0.28).clamp(16.0, 24.0);
      borderRadius = 12.0;
      verticalPadding = fieldSize * 0.25;
    } else if (isTablet) {
      // Tablet - medium screens, balanced sizing
      fieldSize = (fieldAvailableWidth * 0.85).clamp(30.0, 40.0);
      fontSize = (fieldSize * 0.3).clamp(14.0, 20.0);
      borderRadius = 11.0;
      verticalPadding = fieldSize * 0.22;
    } else {
      // Mobile - calculate based on available space
      fieldSize = (fieldAvailableWidth * 0.9).clamp(30.0, 40.0);
      fontSize = (fieldSize * 0.32).clamp(12.0, 18.0);
      borderRadius = 10.0;
      verticalPadding = fieldSize * 0.2;

      // Additional mobile-specific adjustments
      if (screenWidth < 350) {
        // Very small phones
        fieldSize = fieldSize * 0.9;
        fontSize = fontSize * 0.95;
      }
    }

    // Ensure minimum touch target size (44px for accessibility)
    fieldSize = fieldSize.clamp(44.0, double.infinity);

    // Calculate height proportionally but ensure it's not too tall
    final fieldHeight = (fieldSize * 1.2).clamp(44.0, fieldSize * 1.5);

    return SizedBox(
      width: fieldSize,
      height: fieldHeight,
      child: TextField(
        controller: controller,
        autofocus: true,
        onChanged: (value) {
          if (value.length == 1) {
            FocusScope.of(context).nextFocus();
          }
        },
        keyboardType: TextInputType.number,
        textAlign: TextAlign.center,
        cursorColor: AppColors.blackTextColor,
        maxLength: 1,
        style: TextStyle(
          color: AppColors.primaryLightTextColor,
          fontSize: fontSize,
          fontFamily: "Inter-Regular",
          fontWeight: FontWeight.w600,
        ),
        decoration: InputDecoration(
          counterText: "", // Hide the counter
          hintStyle: TextStyle(
            color: AppColors.primaryLightTextColor,
            fontSize: fontSize,
            fontFamily: "Inter-Regular",
            fontWeight: FontWeight.w400,
          ),
          contentPadding: EdgeInsets.symmetric(
            vertical: verticalPadding,
            horizontal: 0,
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(borderRadius),
            borderSide: const BorderSide(
              color: AppColors.primaryBorderColor,
              width: 2,
            ),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(borderRadius),
            borderSide: BorderSide(
              color: Colors.grey[300]!,
              width: 2,
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(borderRadius),
            borderSide: const BorderSide(
              color: AppColors.primaryBorderColor,
              width: 2,
            ),
          ),
          isDense: true,
        ),
      ),
    );
  }
}
