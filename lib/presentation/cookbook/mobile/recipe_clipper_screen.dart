import 'dart:async';
import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/core/widgets/custom_appbar.dart';
import 'package:mastercookai/core/widgets/custom_appbar_mobile.dart';
import 'package:mastercookai/core/widgets/custom_button.dart';
import 'package:mastercookai/core/providers/recipe_clipper_cookbook_notifier.dart';
import 'package:mastercookai/core/data/models/cookbook.dart';
import 'package:mastercookai/core/widgets/custom_input_field.dart';
import 'package:mastercookai/core/widgets/custom_text.dart';
import '../../../app/imports/packages_imports.dart';
import '../../../core/network/app_status.dart';
import '../../../core/providers/cookbook_notifier.dart';
import '../../../core/utils/Utils.dart';
import '../../../core/utils/device_utils.dart';
import '../../../core/widgets/clipper_drop_dpwn.dart';
import '../cookbook_screen.dart';

final cookbookNameProvider = StateProvider<String>((ref) => '');
final isCookbookCreatedProvider = StateProvider<bool>((ref) => false);
final importRecipeLoadingProvider = StateProvider<bool>((ref) => false);

class RecipeClipperScreen extends HookConsumerWidget {
  const RecipeClipperScreen({super.key});

  // Regular expression for basic URL validation
  static final RegExp _urlRegExp = RegExp(
    r'^(https?:\/\/)?' // Optional http or https
    r'((([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,})|' // Domain name (e.g., example.com)
    r'(localhost))' // or localhost
    r'(:\d+)?' // Optional port
    r'(\/[^\s]*)?$', // Optional path
    caseSensitive: false,
  );

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final TextEditingController urlController = useTextEditingController();
    final TextEditingController descriptionController = useTextEditingController();
    final isDropdownOpen = useState<bool>(false);
    final isUrlValid = useState<bool>(true);
    final isUrlEmpty = useState<bool>(true);
    final selectedCookbook = useState<Cookbook?>(null);

    // Fetch cookbooks on screen open
    useEffect(() {
      //ref.read(recipeClipperCookbookNotifierProvider.notifier).fetchCookbooks(context);
      return () {
        // urlController.dispose();
        // descriptionController.dispose();
      };
    }, []);

    final cookbookState = ref.watch(recipeClipperCookbookNotifierProvider);
    final isImportLoading = ref.watch(importRecipeLoadingProvider);

    // Validate URL on change
    void validateUrl(String value) {
      isUrlEmpty.value = value.trim().isEmpty;
      isUrlValid.value = value.trim().isEmpty || _urlRegExp.hasMatch(value.trim());
    }

    // Clear controllers and reset state
    void clearControllers() {
      urlController.clear();
      descriptionController.clear();
      isUrlEmpty.value = true;
      isUrlValid.value = true;
      selectedCookbook.value = null;
    }

    return Scaffold(
      appBar: CustomAppBar(title: 'Recipe Clipper',),
      body: Container(
        color: Colors.white,
        padding: EdgeInsets.symmetric(horizontal: 30.w, vertical: 20.h),
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 50.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    CustomText(
                      text: "To Import recipe please enter Url",
                      color: AppColors.primaryGreyColor,
                      size: 15,
                      weight: FontWeight.w500,
                      overflow: TextOverflow.ellipsis,
                      underline: true,
                    ),
                    SizedBox(height: 20.h),
                    CustomInputField(
                      hintText: "Enter recipe URL",
                      controller: urlController,
                      onChanged: validateUrl,
                      maxLines: 3,
                    ),
                    if (!isUrlValid.value && !isUrlEmpty.value)
                      Padding(
                        padding: EdgeInsets.only(top: 8.h),
                        child: Text(
                          "Please enter a valid URL",
                          style: TextStyle(
                            color: Colors.red,
                            fontSize: 14.sp,
                          ),
                        ),
                      ),
                    SizedBox(height: 20.h),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        CustomText(
                          text: "Select Your Cookbook",
                          color: AppColors.primaryGreyColor,
                          size: 14,
                          weight: FontWeight.w600,
                          underline: true,
                        ),
                        MouseRegion(
                          cursor: SystemMouseCursors.click,
                          child: GestureDetector(
                            onTap: () {
                              if (!isDropdownOpen.value) {
                                ref
                                    .read(cookbookNotifierProvider.notifier)
                                    .showCookbookBottomSheet(
                                  context: context,
                                  ref: ref,
                                );
                              // showCookbookDialog(context, ref);
                              }
                            },
                            child: CustomText(
                              text: "+ Add New CookBook",
                              color: AppColors.primaryGreyColor,
                              size: 14,
                              weight: FontWeight.w600,
                              underline: true,
                            ),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 10.h),
                    ClipperDropDpwn(
                      hint: "Search Cookbook",
                      selectedValue: selectedCookbook.value,
                      items: cookbookState.data ?? [],
                      cookbookState: cookbookState,
                      onChanged: (val) {
                        selectedCookbook.value = val;
                        print("Selected Cookbook: ${val?.id} - ${val?.name}");
                      },
                      onOpenStateChanged: (isOpen) {
                        isDropdownOpen.value = isOpen;
                      },
                      icon: SvgPicture.asset(
                        AssetsManager.search,
                        height: 15,
                        width: 15,
                      ),
                    ),
                    SizedBox(height: 20.h),
                    CustomText(
                      text: "Add a Note (Optional)",
                      color: AppColors.primaryGreyColor,
                      size: 14,
                      weight: FontWeight.w400,
                      overflow: TextOverflow.ellipsis,
                      underline: true,
                    ),
                    SizedBox(height: 20.h),
                    CustomInputField(
                      controller: descriptionController,
                      maxLines: 4,
                      hintText:
                          "E.g., reduce salt, try with tofu instead of chicken...",
                    ),
                    SizedBox(height: 40.h),
                    Center(
                      child: CustomButton(
                        text: 'Import Recipe',
                        height: 30.h,
                        isLoading: isImportLoading,
                        borderRadius: 8,
                        width: 250,
                        fontSize: DeviceUtils().isTabletOrIpad(context)
                            ? 16.sp
                            : responsiveFont(18),
                        onPressed: () async {
                          if (urlController.text.trim().isEmpty) {
                            Utils().showFlushbar(
                              context,
                              message: 'Please enter a recipe URL',
                              isError: true,
                            );
                            isUrlEmpty.value = true;
                            return;
                          }
                          if (!_urlRegExp.hasMatch(urlController.text.trim())) {
                            Utils().showFlushbar(
                              context,
                              message: 'Please enter a valid URL',
                              isError: true,
                            );
                            isUrlValid.value = false;
                            return;
                          }
                          if (selectedCookbook.value == null) {
                            Utils().showFlushbar(
                              context,
                              message: 'Please select a cookbook',
                              isError: true,
                            );
                            return;
                          }

                          ref.read(importRecipeLoadingProvider.notifier).state =
                              true;

                          final result = await ref
                              .read(recipeClipperCookbookNotifierProvider.notifier)
                              .recipeClipper(
                                context,
                                urlController.text.trim(),
                                selectedCookbook.value,
                              );

                          ref.read(importRecipeLoadingProvider.notifier).state =
                              false;

                          if (result != null) {
                            var description = descriptionController.text.trim();
                            await context.push(
                              '/cookbook/cookbookDetail/addRecipe',
                              extra: {
                                'selectedCookbook': selectedCookbook.value,
                                'callFromClipper': true,
                                'clipperNote': description,
                                'recipeDetails': result,
                              },
                            );
                            clearControllers();
                          }
                        },
                      ),
                    ),
                    SizedBox(height: 10.h),
                    Visibility(
                      visible: cookbookState.status == AppStatus.error,
                      child: Center(
                        child: TextButton(
                          onPressed: () async {
                            if (selectedCookbook.value == null) {
                              Utils().showFlushbar(
                                context,
                                message: 'Please select a cookbook',
                                isError: true,
                              );
                              return;
                            }
                            var description = descriptionController.text.trim();
                            await context.push(
                              '/cookbook/cookbookDetail/addRecipe',
                              extra: {
                                'selectedCookbook': selectedCookbook.value,
                                'callFromClipper': false,
                                'clipperNote': description,
                              },
                            );
                            clearControllers();
                          },
                          child: CustomText(
                            text: 'Add Manually',
                            color: AppColors.primaryColor,
                            weight: FontWeight.w400,
                            decoration: TextDecoration.underline,
                            decorationColor: AppColors.primaryColor,
                            decorationThickness: 1.0,
                            size: 18.sp,
                            underline: true,
                          ),
                        ),
                      ),
                    ),
                    SizedBox(height: 24.h),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}