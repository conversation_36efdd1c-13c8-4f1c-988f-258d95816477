import 'dart:io';

import 'package:flutter_svg/svg.dart';
import 'package:mastercookai/app/imports/core_imports.dart';

import '../../../../app/imports/packages_imports.dart';
import '../../../../core/network/app_status.dart';
import '../../../../core/providers/cookbook_notifier.dart';
import '../../../../core/utils/Utils.dart';
import '../../../../core/utils/device_utils.dart';
import '../../../../core/widgets/custom_button.dart';
import '../../../../core/widgets/custom_input_field.dart';
import '../../../../core/widgets/custom_text.dart';

class CookbookBottomSheetContent extends HookConsumerWidget {
  final String title;
  final String hintText;
  final String successText;
  final String buttonText;
  final String successButtonText;
  final bool callFromUpdate;
  final bool isScrollable;
  final VoidCallback? onSuccess;
  final Function(String, BuildContext, WidgetRef)? onCreate;
  final String? cookbookId;
  final String? cookbookName;
  final ScrollController scrollController;

  const CookbookBottomSheetContent({
    super.key,
    required this.title,
    required this.hintText,
    required this.successText,
    required this.buttonText,
    required this.successButtonText,
    this.callFromUpdate = false,
    this.isScrollable = false,
    this.onSuccess,
    this.onCreate,
    this.cookbookId,
    this.cookbookName,
    required this.scrollController,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final cookbookNotifier = ref.read(cookbookNotifierProvider.notifier);
    final appState = ref.watch(cookbookNotifierProvider);
    final nameController = useTextEditingController(text: cookbookName ?? '');
    final formKey = useMemoized(() => GlobalKey<FormState>());

    ref.listen(cookbookNotifierProvider, (previous, next) {
      if (next.status == AppStatus.error && next.errorMessage != null) {
        Utils().showSnackBar(context, next.errorMessage!);
      }
    });

    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      decoration: BoxDecoration(
        color: context.theme.scaffoldBackgroundColor,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(26)),
      ),
      padding: EdgeInsets.only(
        left: 25,
        right: 25,
        top: 10, // Reduced top padding to accommodate drag handle
        bottom: MediaQuery.of(context).viewInsets.bottom + 20,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
// Drag handle
          Container(
            margin: const EdgeInsets.only(top: 10.0),
            width: 40.0,
            height: 5.0,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2.5),
            ),
          ),
          Flexible(
            child: SingleChildScrollView(
              child: appState.status ==
                      (callFromUpdate
                          ? AppStatus.updateSuccess
                          : AppStatus.createSuccess)
                  ? _buildSuccessContent(context, ref)
                  : buildFormContent(
                      context, ref, nameController, formKey, cookbookNotifier),
            ),
          ),
        ],
      ),
    );
  }

  Widget buildFormContent(
      BuildContext context,
      WidgetRef ref,
      TextEditingController controller,
      GlobalKey<FormState> formKey,
      CookbookNotifier cookbookNotifier) {
    final appState = ref.watch(cookbookNotifierProvider);

    return SingleChildScrollView(
      physics: isScrollable ? null : const NeverScrollableScrollPhysics(),
      controller: scrollController,
      child: Form(
        key: formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            //  _closeButton(context, ref),
            const SizedBox(height: 16),
            CustomText(
              text: title,
              color: AppColors.blackTextColor,
              size: 24,
              weight: FontWeight.w700,
            ),
            const SizedBox(height: 30),
            CustomInputField(
              hintText: hintText,
              controller: controller,
              keyboardType: TextInputType.text,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Please enter cookbook name';
                }
                if (value.trim().length < 3) {
                  return 'Cookbook name must be at least 3 characters';
                }
                if (value.trim().length > 100) {
                  return 'Cookbook name must not exceed 100 characters';
                }
                if (RegExp(r'\s{2,}').hasMatch(value)) {
                  return 'Cookbook name should not contain multiple spaces in a row';
                }
                if (value != value.trim()) {
                  return 'Cookbook name should not start or end with a space';
                }
                return null;
              },
            ),
            const SizedBox(height: 30),
            CustomButton(
              width: 200,
              fontSize: 14,
              weight: FontWeight.w600,
              borderRadius: 10,
              text: buttonText,
              isLoading: appState.status ==
                  (callFromUpdate ? AppStatus.updating : AppStatus.creating),
              onPressed: () async {
                if (formKey.currentState!.validate()) {
                  try {
                    if (callFromUpdate) {
                      await cookbookNotifier.updateCookbook(
                        id: cookbookId!,
                        name: controller.text.trim(),
                        filePath: File(''), // Changed from File('')
                        context,
                        callFromUpdate: false,
                      );
                    } else {
                      await cookbookNotifier.createCookbook(
                        context,
                        controller.text.trim(),
                      );
                    }
                    await onCreate!(controller.text.trim(), context, ref);
                  } catch (e) {
                    Utils().showSnackBar(context, 'Error: $e');
                  }
                }
              },
            ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildSuccessContent(BuildContext context, WidgetRef ref) {
    return SizedBox(
      width: MediaQuery.of(context).size.width,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const SizedBox(height: 16),
          // _closeButton(context, ref),
          SvgPicture.asset(AssetsManager.success, height: 80, width: 80),
          const SizedBox(height: 30),
          CustomText(
            text: successText,
            color: AppColors.primaryGreyColor,
            size: 14,
            weight: FontWeight.w400,
            align: TextAlign.center,
          ),
          const SizedBox(height: 30),
          CustomButton(
            width: 250,
            fontSize: 14,
            borderRadius: 10,
            weight: FontWeight.w600,
            isLoading:
                ref.watch(cookbookNotifierProvider).status == AppStatus.loading,
            text: successButtonText,
            onPressed: () {
              Navigator.of(context).pop();
              ref
                  .read(cookbookNotifierProvider.notifier)
                  .fetchCookbooks(context: context);
              if (onSuccess != null) {
                onSuccess!();
              }
            },
          ),
        ],
      ),
    );
  }

  Widget _closeButton(BuildContext context, WidgetRef ref) {
    return Align(
      alignment: Alignment.topRight,
      child: IconButton(
        icon: const Icon(IconsaxPlusBold.close_circle, color: Colors.red),
        onPressed: () {
          ref.read(cookbookNotifierProvider.notifier).resetToIdle();
          Navigator.of(context).pop();
        },
      ),
    );
  }
}
