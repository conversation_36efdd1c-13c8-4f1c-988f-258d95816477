import 'dart:ui';

import 'package:flutter_svg/svg.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/core/widgets/custom_text.dart';
import '../../../../../app/imports/packages_imports.dart';
import '../../../../../core/widgets/custom_button.dart';
import '../../../../core/widgets/custom_ccon_text_button.dart';
import '../../../recipe/subview/custom_sub_media_widget.dart';

class ImportCreateCardMobileList extends StatelessWidget {
  final VoidCallback onImport;
  final VoidCallback? onAddFromUrl;
  final VoidCallback onCreate;
  final bool isRecipe;
  final String title;
  final bool? isAddRecipe;
  final String importText;

  const ImportCreateCardMobileList({
    super.key,
    required this.onImport,
    this.onAddFromUrl,
    required this.onCreate,
    required this.isRecipe,
    required this.title,
    this.isAddRecipe = false,
    required this.importText,
  });

  @override
  Widget build(BuildContext context) {
     return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(15),
      ),
      child: Card(
        elevation: 5,
        color: context.theme.cardColor,
        margin: EdgeInsets.zero,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(15),
        ),
        child: Padding(
          padding: const EdgeInsets.all(6.0),
          child: CustomPaint(
            painter: DottedBorderPainter(),
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Expanded(
                        flex: 1,
                        child: CustomIconTextButton(
                          iconPath: AssetsManager.mynaui_file,
                          text: importText,
                          onTap: onImport,
                          iconColor: Colors.red,
                          textColor: AppColors.primaryGreyColor,
                          borderColor: AppColors.lightestGreyColor,
                        ),
                      ),
                      SizedBox(
                        width: 10,
                      ),
                      Expanded(
                        flex: 1,
                        child: (isRecipe ?? false)
                            ? CustomIconTextButton(
                                iconPath: AssetsManager.add_link,
                                text: 'Import From URL',
                                onTap: onAddFromUrl,
                                iconColor: Colors.red,
                                textColor: AppColors.primaryGreyColor,
                                borderColor: AppColors.lightestGreyColor,
                              )
                            : CustomButton(
                                text: title,
                                fontSize: 12.0,
                                onPressed: onCreate,
                                borderRadius: 5.0,
                              ),
                      ),
                    ],
                  ),
                  Visibility(
                      visible: isRecipe ?? false,
                      child: const SizedBox(height: 10.0)),
                  Visibility(
                    visible: isRecipe ?? false,
                    child: CustomButton(
                      text: title,
                      fontSize: 12.0,
                      onPressed: onCreate,
                      borderRadius: 5.0,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
