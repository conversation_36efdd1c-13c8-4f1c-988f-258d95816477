import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/core/widgets/custom_text.dart';
import '../../../../../core/widgets/custom_doted_lines.dart';
import '../../../../app/imports/packages_imports.dart';
import '../../../../core/data/models/recipe_response.dart';
import '../../../../core/helpers/date_formatter.dart';
import '../../../../core/providers/recipe_notifier.dart';
import '../../../../core/utils/screen_sizer.dart';
import '../../../../core/widgets/common_image.dart';
import '../../../../core/widgets/custom_hover_menu.dart';

class RecipeGridItem extends ConsumerWidget {
  final Recipe recipe;
  final VoidCallback onPressed;
  final int? cookbookId;
  final VoidCallback? onDeleted; // Add callback for when recipe is deleted

  const RecipeGridItem(
      {super.key,
      required this.recipe,
      required this.onPressed,
      this.cookbookId,
      this.onDeleted});

  @override
  Widget build(BuildContext context, WidgetRef ref) {

    return GestureDetector(
      onTap: onPressed,
      child: SizedBox(
        height: 220,
        child: Stack(
          children: [
            Card(
              elevation: 4,
              color: context.theme.cardColor,
              shape:
                  RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
              clipBehavior: Clip.hardEdge,
              child: Padding(
                padding: EdgeInsets.all(8),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    /// Image with badge and bottom overlay
                    Stack(
                      children: [
                        /// Recipe Image
                        ClipRRect(
                          borderRadius: BorderRadius.circular(12),
                          child: recipe.recipeMedia != null &&
                                  recipe.recipeMedia!.isNotEmpty &&
                                  recipe.coverMediaIndex != null &&
                                  recipe.coverMediaIndex! <
                                      recipe.recipeMedia!.length
                              ? CommonImage(
                                  imageSource: recipe
                                          .recipeMedia![recipe.coverMediaIndex]
                                          .mediaUrl ??
                                      '',
                                  placeholder: AssetsManager.recipe_place_holder,
                                  width: double.infinity,
                                  height: ScreenSizer()
                                      .calculateRecipeImageHeight(context),
                                  fit: BoxFit.cover,
                                )
                              : CommonImage(
                                  imageSource: AssetsManager.recipe_place_holder,
                                  width: double.infinity,
                                  height: ScreenSizer()
                                      .calculateRecipeImageHeight(context),
                                  fit: BoxFit.cover,
                                ),
                        ),

                        /// Bottom Overlay: Time & Persons
                        Positioned(
                          bottom: 0,
                          left: 0,
                          right: 0,
                          child: Container(
                            decoration: BoxDecoration(
                              color: Colors.black.withValues(alpha: 0.5),
                              borderRadius: const BorderRadius.vertical(
                                  bottom: Radius.circular(12)),
                            ),
                            padding: EdgeInsets.symmetric(
                                horizontal: 14.w, vertical: 15.h),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                              children: [
                                /// Time Section
                                Row(
                                  children: [
                                    SvgPicture.asset(
                                      AssetsManager.ic_time,
                                      height: 16,
                                      width: 16,
                                    ),
                                    SizedBox(width: 4),
                                    CustomText(
                                      text: recipe.totalTime ?? '',
                                      color: Colors.white,
                                      size: 10,
                                      weight: FontWeight.w400,
                                    ),
                                  ],
                                ),

                                /// Vertical Divider
                                Container(
                                  width: 1.w,
                                  height: 20.h,
                                  color: Colors.white,
                                ),

                                /// Person Section
                                Row(
                                  children: [
                                    SvgPicture.asset(
                                      AssetsManager.dining,
                                      height: 16,
                                      width: 16,
                                    ),
                                    SizedBox(width: 4),
                                    CustomText(
                                      text: "${recipe.servings} persons",
                                      color: Colors.white,
                                      size: 10,
                                      weight: FontWeight.w400,
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),

                    /// Below image content
                    SizedBox(height: 16),

                    Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: Text(
                            recipe.name,
                            style: context.theme.textTheme.bodyMedium!.copyWith(
                              color: AppColors.primaryGreyColor,
                              fontSize: 16, //responsiveFont(28).sp,
                              fontWeight: FontWeight.w400,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        CustomHoverMenu(
                          items: ['Delete'],
                          itemIcons: [Icons.delete],
                          // Icons for each item
                          onItemSelected: (value) async {
                            if (value == 'Delete') {
                              final recipeNotifier =
                                  ref.read(recipeNotifierProvider.notifier);

                              final success = await recipeNotifier.deleteRecipe(
                                  context: context,
                                  cookbookId: cookbookId ?? 0,
                                  recipeId: recipe.id);

                              // Call the onDeleted callback if deletion was successful
                              if (success && onDeleted != null) {
                                onDeleted!();
                              }
                            }
                          },
                          menuWidth: 140.0,
                          // Matches previous menu width
                          menuTitle: 'Show Menu',
                          // Tooltip on hover
                          triggerIcon:
                              Icons.more_vert_outlined, // Custom trigger icon
                        ),
                      ],
                    ),
                    SizedBox(
                      height: 8,
                    ),

                    CustomPaint(
                      painter: DottedLinePainter(
                        strokeWidth: 1,
                        dashWidth: 6,
                        color: AppColors.lightestGreyColor,
                      ),
                      size: Size(double.infinity, 2),
                    ),
                    Spacer(),
                    Row(
                      children: [
                        SvgPicture.asset(
                          AssetsManager.rating,
                          height: 24.h,
                          width: 24.w,
                        ),
                        SizedBox(width: 4),
                        CustomText(
                          text: "${recipe.reviewsCount}",
                          size: 12,
                          weight: FontWeight.w400,
                          color: AppColors.textGreyColor,
                        ),
                        Spacer(),
                        CustomText(
                          text: DateFormatter.timeAgo(recipe.dateAdded!),
                          size: 12,
                          weight: FontWeight.w400,
                          color: AppColors.textGreyColor,
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),

            /// Top Right SVG Badge
            Positioned(
              top: 8.h,
              right: 20,
              child: SvgPicture.asset(
                AssetsManager.rewards, // your reward.svg asset path
                width: 25,
                height: 34,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
