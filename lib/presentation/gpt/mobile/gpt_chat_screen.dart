import 'dart:async';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/core/data/models/get_ask_ai_response.dart';
import 'package:mastercookai/core/utils/device_utils.dart';
import 'package:mastercookai/presentation/gpt/sidebar.dart';
import 'package:mastercookai/app/imports/packages_imports.dart';
import 'package:mastercookai/core/widgets/custom_appbar.dart';
import 'package:mastercookai/core/widgets/custom_searchbar.dart';
import 'package:mastercookai/core/providers/gpt/ask_ai_notifier.dart';
import 'package:mastercookai/core/providers/gpt/thread_messages_notifier.dart';
import 'package:mastercookai/core/network/app_status.dart';
import 'package:mastercookai/presentation/gpt/mobile/widgets/build_chat_content.dart';
import 'package:mastercookai/presentation/gpt/mobile/thread_list_screen.dart';

final chatProvider =
    StateNotifierProvider<ChatNotifier, List<String>>((ref) => ChatNotifier());

class ChatNotifier extends StateNotifier<List<String>> {
  ChatNotifier() : super([]);

  void sendMessage(String message) {
    if (message.isEmpty) return;
    state = [...state, message, 'This is a response to: $message'];
  }
}

class GptChatScreen extends ConsumerStatefulWidget {
  const GptChatScreen({super.key});

  @override
  ConsumerState<GptChatScreen> createState() => _GptChatScreenState();
}

class _GptChatScreenState extends ConsumerState<GptChatScreen> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  // Sample ingredients list
  final List<String> _ingredients = [
    '1 cup butter, divided',
    '1 onion, minced',
    '1 tablespoon minced garlic',
    '1 (15 ounce) can tomato sauce',
    '3 cups heavy cream',
  ];

  // Selected thread state
  Thread? selectedThread;

  // Track if this is a new chat session
  bool isNewChat = true;

  // Track the current thread ID for the conversation
  int? currentThreadId;

  // ScrollController for chat messages
  final ScrollController _chatScrollController = ScrollController();

  // Search controller and state
  final TextEditingController _searchController = TextEditingController();
  bool _isSearching = false;

  // Message search debounce timer
  Timer? _messageSearchDebounce;

  @override
  void initState() {
    super.initState();
    _chatScrollController.addListener(_chatScrollListener);
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await ref.read(askAiNotifierProvider.notifier).getAskAiThreads(
            context: context,
            loadMore: false,
          );
      final extras = GoRouterState.of(context).extra as Map<String, dynamic>?;
      final Thread? passedThread = extras?['threads'] as Thread?;
      final askAiState = ref.read(askAiNotifierProvider);

      // Only select a thread if one was passed from navigation
      if (passedThread != null &&
          askAiState.data != null &&
          askAiState.data!.isNotEmpty) {
        final index =
            askAiState.data!.indexWhere((cb) => cb.id == passedThread.id);
        if (index != -1) {
          // Set selected thread and fetch its messages
          setState(() {
            selectedThread = passedThread;
            isNewChat = false; // Not a new chat when thread is passed
            currentThreadId = passedThread.id; // Set current thread ID
          });

          // Fetch messages for the selected thread
          if (mounted) {
            await ref
                .read(threadMessagesNotifierProvider.notifier)
                .fetchThreadsMessages(
                  context: context,
                  id: passedThread.id,
                  loadMore: false,
                );
          }
        }
      }
      // If no thread is passed, start in new chat mode (isNewChat = true by default)
    });
  }

  @override
  void dispose() {
    _chatScrollController.dispose();
    _searchController.dispose();
    _messageSearchDebounce?.cancel();
    super.dispose();
  }

  // Handle thread selection from sidebar
  void _onThreadSelected(Thread thread) async {
    setState(() {
      selectedThread = thread;
      isNewChat = false; // No longer a new chat when thread is selected
      currentThreadId = thread.id; // Set current thread ID
    });

    // Clear search when switching threads
    _searchController.clear();
    setState(() {
      _isSearching = false;
    });

    // Fetch messages for the selected thread
    if (mounted) {
      await ref
          .read(threadMessagesNotifierProvider.notifier)
          .fetchThreadsMessages(
            context: context,
            id: thread.id,
            loadMore: false,
          );
    }
    if (_scaffoldKey.currentState?.isDrawerOpen ?? false) {
      Navigator.of(context).pop();
    }
  }

  // Handle message search functionality
  void _onMessageSearchChanged(String query) {
    // Cancel previous debounce timer
    _messageSearchDebounce?.cancel();

    // Set up new debounce timer
    _messageSearchDebounce = Timer(const Duration(milliseconds: 500), () {
      if (mounted && currentThreadId != null) {
        ref.read(threadMessagesNotifierProvider.notifier).searchMessages(
              context: context,
              threadId: currentThreadId!,
              query: query,
            );
      }
    });
  }

  // Chat scroll listener for load more messages
  void _chatScrollListener() {
    // Load more when scrolling to the top (for older messages)
    if (_chatScrollController.position.pixels <= 200) {
      final threadMessagesState = ref.read(threadMessagesNotifierProvider);
      if (threadMessagesState.hasMore &&
          threadMessagesState.status != AppStatus.loadingMore &&
          currentThreadId != null) {
        ref.read(threadMessagesNotifierProvider.notifier).fetchThreadsMessages(
              context: context,
              id: currentThreadId!,
              loadMore: true,
              search: _searchController.text.trim().isEmpty
                  ? null
                  : _searchController.text.trim(),
            );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final askAiNotifier = ref.read(askAiNotifierProvider.notifier);
    // final formKey = useMemoized(() => GlobalKey<FormState>());

    // Auto-scroll when askAI response is received
    ref.listen<AppState<List<Thread>>>(askAiNotifierProvider, (previous, next) {
      if (previous?.status == AppStatus.creating &&
          next.status == AppStatus.createSuccess) {
        // Response received, scroll to bottom to show the new message
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (_chatScrollController.hasClients) {
            _chatScrollController.animateTo(
              _chatScrollController.position.maxScrollExtent,
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeOut,
            );
          }
        });
      }
    });
    final messageController = TextEditingController();
    final screenSize = MediaQuery.of(context).size;
    
    return Scaffold(
      key: _scaffoldKey,
      appBar: CustomAppBar(
        title: "Recipe GPT",
        actions: [
           if (!isNewChat)
          _isSearching
              ? CustomSearchBar(
                  width: 220,
                  height: 64.h,
                  controller: _searchController,
                  onChanged: _onMessageSearchChanged,
                  onClear: () {
                    setState(() {
                      _isSearching = false;
                      _searchController.clear();
                      ref
                          .read(threadMessagesNotifierProvider.notifier)
                          .searchMessages(
                            context: context,
                            threadId: currentThreadId!,
                            query: '',
                          );
                    });
                  },
                )
              : IconButton(
                  icon: Icon(
                    Icons.search,
                    size: 24,
                    color: const Color.fromARGB(255, 130, 130, 130),
                  ),
                  onPressed: () {
                    setState(() {
                      _isSearching = true;
                    });
                  },
                  tooltip: 'Search Recipes',
                ),

          Padding(
              padding: const EdgeInsets.only(right: 8.0),
              child: GestureDetector(
                onTap: () {
                  // Start a new chat session
                  setState(() {
                    selectedThread = null;
                    isNewChat = true;
                    currentThreadId = null; // Reset current thread ID
                  });

                  // Clear search when starting new chat
                  _searchController.clear();
                  setState(() {
                    _isSearching = false;
                  });

                  // Clear the thread messages
                  ref.read(threadMessagesNotifierProvider.notifier).clearMessages();
                },
                child: Row(
                  children: [
                    Image.asset(AssetsManager.gpt_gray_edit, width: 24, height: 24),
                    SizedBox(width: 8.w),
                  ],
                ),
              ),
            ),

            Padding(
                padding: const EdgeInsets.only(right: 8.0),
                child: GestureDetector(
                onTap: () async {
                  final result = await Navigator.push(
                    context,
                    MaterialPageRoute(builder: (context) => const ThreadListScreen()),
                  );
                  if (result is Thread) {
                    _onThreadSelected(result);
                  }
                },
                child: Row(
                  children: [
                    Image.asset(AssetsManager.gpt_history, width: 24, height: 24),
                    SizedBox(width: 8.w),
                  ],
                ),
              ),
            )
        ],
      ),
      body: Stack(
        fit: StackFit.expand,
        children: [
          Image.asset(
            AssetsManager.background_img,
            fit: BoxFit.cover,
          ),
          LayoutBuilder(
            builder: (context, constraints) {
              final isSmallScreen = constraints.maxWidth <= 1100;
              return Row(
                children: [
                  if (!isSmallScreen)
                    Sidebar(
                      onThreadSelected: _onThreadSelected,
                    ),
                  Expanded(
                    //flex: isSmallScreen ? 1 : 7,
                    child: Container(
                      decoration: BoxDecoration(
                        color: Colors.white,
                      ),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: Consumer(
                              builder: (context, ref, child) {
                                final threadMessagesState = ref.watch(threadMessagesNotifierProvider);
                                return buildChatContent(threadMessagesState, _chatScrollController, ref, isNewChat, context, _isSearching);
                              },
                            ),
                          ),
                          Container(
                            // margin: EdgeInsets.symmetric(
                            //     horizontal: 10, vertical: 20),
                                margin: EdgeInsets.only(top: 2, bottom: 40, left: 10, right: 10),

                              decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(color: Colors.grey[200]!),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withValues(alpha: 0.09),
                                  blurRadius: 10,
                                  offset: const Offset(0, 4),
                                ),
                              ],
                            ),
                            child: Container(
                              margin: EdgeInsets.symmetric(
                                  horizontal: 4, vertical: 0),
                              child: Column(
                                children: [
                                  SizedBox(
                                    height: 2,
                                  ),
                                  Row(
                                    children: [
                                      Expanded(
                                        child: TextFormField(
                                          style: context
                                              .theme.textTheme.labelMedium!
                                              .copyWith(fontSize: 14),
                                          controller: messageController,
                                          decoration: InputDecoration(
                                            filled: false,
                                            // ensure this is false
                                            fillColor: Colors.transparent,
                                            hintText:
                                                'Give cake Recipe with image',
                                            hintStyle: context.theme
                                                .inputDecorationTheme.hintStyle!
                                                .copyWith(
                                                    fontWeight: FontWeight.w400,
                                                    fontSize:14),
                                            errorStyle: context
                                                .theme
                                                .inputDecorationTheme
                                                .errorStyle,
                                            border: InputBorder.none,
                                            focusedBorder: InputBorder.none,
                                            enabledBorder: InputBorder.none,
                                            disabledBorder: InputBorder.none,
                                            isDense: true,
                                            contentPadding:
                                                EdgeInsets.symmetric(
                                                    horizontal: 12,
                                                    vertical: 14),
                                          ),
                                          inputFormatters: [
                                            FilteringTextInputFormatter.allow(
                                                RegExp(r'[a-zA-Z0-9\s,]')),
                                          ],
                                          onFieldSubmitted: (value) {
                                            if (value.trim().isEmpty) return;

                                            // Determine thread ID to use:
                                            // - For new chat: use 0 (creates new thread)
                                            // - For existing conversation: use currentThreadId or selectedThread?.id
                                            final threadIdToUse = isNewChat
                                                ? 0
                                                : (currentThreadId ??
                                                    selectedThread?.id);

                                            askAiNotifier.askAi(
                                                context, threadIdToUse, value,
                                                onResponse: (threadData) {
                                              // Always update currentThreadId from response
                                              setState(() {
                                                currentThreadId = threadData.id;
                                                isNewChat = false; // Set to false after we have the thread ID
                                              });
                                            }, onNewThreadCreated:
                                                    (threadData) {
                                              // When a new thread is created, find and select it
                                              final askAiState = ref
                                                  .read(askAiNotifierProvider);
                                              if (askAiState.data != null &&
                                                  askAiState.data!.isNotEmpty) {
                                                // Find the thread that matches the new thread data
                                                final newThread =
                                                    askAiState.data!.firstWhere(
                                                  (thread) =>
                                                      thread.id ==
                                                      threadData.id,
                                                  orElse: () =>
                                                      askAiState.data!.first,
                                                );
                                                setState(() {
                                                  selectedThread = newThread;
                                                });
                                              }
                                            });
                                            messageController.clear();
                                          },
                                          keyboardType: TextInputType.text,
                                        ),
                                      ),
                                      SizedBox(width: 2),
                                    ],
                                  ),
                                  SizedBox(
                                    height: 6,
                                  ),
                                  Row(
                                    children: [
                                      Visibility(
                                        visible: false,
                                        child: Container(
                                          padding: EdgeInsets.symmetric(
                                              horizontal: 20.w, vertical: 10.h),
                                          decoration: BoxDecoration(
                                            color: Colors.white,
                                            borderRadius:
                                                BorderRadius.circular(10),
                                            border: Border.all(
                                                color: Colors.grey[200]!),
                                            boxShadow: [
                                              BoxShadow(
                                                color: Colors.black
                                                    .withOpacity(0.05),
                                                blurRadius: 10,
                                                offset: const Offset(0, 4),
                                              ),
                                            ],
                                          ),
                                          child: Row(
                                            children: [
                                              SvgPicture.asset(
                                                  AssetsManager.think,
                                                  width: 35.w,
                                                  height: 35.h),
                                              SizedBox(
                                                width: 10.w,
                                              ),
                                              Text("Think before responding",
                                                  style: context.theme.textTheme
                                                      .bodyMedium!
                                                      .copyWith(
                                                          color: AppColors
                                                              .primaryGreyColor,
                                                          fontWeight:
                                                              FontWeight.w400,
                                                          fontSize: 14))
                                            ],
                                          ),
                                        ),
                                      ),
                                      SizedBox(
                                        width: 8,
                                      ),
                                      Visibility(
                                          visible: false,
                                          child: Container(
                                            padding: EdgeInsets.symmetric(
                                                horizontal: 20.w,
                                                vertical: 10.h),
                                            decoration: BoxDecoration(
                                              color: Colors.white,
                                              borderRadius:
                                                  BorderRadius.circular(10),
                                              border: Border.all(
                                                  color: Colors.grey[200]!),
                                              boxShadow: [
                                                BoxShadow(
                                                  color: Colors.black
                                                      .withOpacity(0.05),
                                                  blurRadius: 10,
                                                  offset: const Offset(0, 4),
                                                ),
                                              ],
                                            ),
                                            child: Row(
                                              children: [
                                                SvgPicture.asset(
                                                    AssetsManager.web_search,
                                                    width: 35.w,
                                                    height: 35.h),
                                                SizedBox(
                                                  width: 10.w,
                                                ),
                                                Text("Search the web",
                                                    style: context.theme
                                                        .textTheme.bodyMedium!
                                                        .copyWith(
                                                            color: AppColors
                                                                .primaryGreyColor,
                                                            fontWeight:
                                                                FontWeight.w400,
                                                            fontSize: 14))
                                              ],
                                            ),
                                          )),
                                      Spacer(),
                                      GestureDetector(
                                        child: Container(
                                          padding: EdgeInsets.symmetric(
                                              horizontal: 8, vertical: 6),
                                          decoration: BoxDecoration(
                                            color: Colors.white,
                                            borderRadius:
                                                BorderRadius.circular(10),
                                            border: Border.all(
                                                color: Colors.grey[200]!),
                                            boxShadow: [
                                              BoxShadow(
                                                color: Colors.black
                                                    .withValues(alpha: 0.05),
                                                blurRadius: 10,
                                                offset: const Offset(0, 4),
                                              ),
                                            ],
                                          ),
                                          child: SvgPicture.asset(
                                              AssetsManager.ic_send_msg,
                                              width: 35.w,
                                              height: 35.h),
                                        ),
                                        onTap: () {
                                          if (messageController.text
                                              .trim()
                                              .isEmpty) return;

                                          // Determine thread ID to use:
                                          // - For new chat: use 0 (creates new thread)
                                          // - For existing conversation: use currentThreadId or selectedThread?.id
                                          final threadIdToUse = isNewChat
                                              ? 0
                                              : (currentThreadId ??
                                                  selectedThread?.id);

                                          askAiNotifier.askAi(
                                              context,
                                              threadIdToUse,
                                              messageController.text.toString(),
                                              onResponse: (threadData) {
                                            // Always update currentThreadId from response
                                            setState(() {
                                              currentThreadId = threadData.id;
                                              isNewChat =
                                                  false; // Set to false after we have the thread ID
                                            });
                                          }, onNewThreadCreated: (threadData) {
                                            // When a new thread is created, find and select it
                                            final askAiState =
                                                ref.read(askAiNotifierProvider);
                                            if (askAiState.data != null &&
                                                askAiState.data!.isNotEmpty) {
                                              // Find the thread that matches the new thread data
                                              final newThread =
                                                  askAiState.data!.firstWhere(
                                                (thread) =>
                                                    thread.id == threadData.id,
                                                orElse: () =>
                                                    askAiState.data!.first,
                                              );
                                              setState(() {
                                                selectedThread = newThread;
                                              });
                                            }
                                          });

                                          // Auto-scroll to bottom to show thinking loader
                                          Future.delayed(
                                              const Duration(milliseconds: 100),
                                              () {
                                            if (_chatScrollController
                                                .hasClients) {
                                              _chatScrollController.animateTo(
                                                _chatScrollController
                                                    .position.maxScrollExtent,
                                                duration: const Duration(
                                                    milliseconds: 300),
                                                curve: Curves.easeOut,
                                              );
                                            }
                                          });

                                          // ref
                                          //     .read(chatProvider.notifier)
                                          //     .sendMessage(
                                          //         messageController.text);
                                          messageController.clear();
                                        },
                                      ),
                                      SizedBox(width: 20.w),
                                    ],
                                  ),
                                  SizedBox(
                                    height: 20.h,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              );
            },
          ),
        ],
      ),
    );
  }
}
