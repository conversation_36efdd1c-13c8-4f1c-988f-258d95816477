import 'dart:ui';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mastercookai/core/providers/menus/bottom_nav_notifier.dart';
import '../../../app/imports/core_imports.dart';
import '../../../app/imports/packages_imports.dart';
import '../../core/utils/device_utils.dart';

class BottomNavScreen extends HookConsumerWidget {
  final StatefulNavigationShell navigationShell;

  const BottomNavScreen({super.key, required this.navigationShell});

  final List<String> iconAssets = const [
    AssetsManager.home_svg,
    AssetsManager.cookbook_svg,
  // AssetsManager.gpt_svg,
  //  AssetsManager.meals_svg,
    AssetsManager.shopping_svg,
   // AssetsManager.sync_svg,
   //  AssetsManager.tips_svg,
   //  AssetsManager.media_svg,
   //  AssetsManager.fav_svg,
   //  AssetsManager.backup_svg,
    AssetsManager.myProfile,
   ];



  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final bottomNavNotifier = ref.read(bottomNavProvider.notifier);

    useEffect(() {
      bottomNavNotifier.setNavigationShell(navigationShell);
      return null;
    }, [navigationShell]);
    return Scaffold(
      body: navigationShell,
      extendBody: true,
      bottomNavigationBar: Align(
        alignment: Alignment.bottomCenter,
        child: Padding(
          padding: EdgeInsets.only(bottom: 40.h,left: DeviceUtils().isTabletOrIpad(context)?20:0,right: DeviceUtils().isTabletOrIpad(context)?20:0),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(10),
            child: BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 4.h),
                decoration: BoxDecoration(
                  color: AppColors.lightestGreyColor.withValues(alpha: .2),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    children: List.generate(iconAssets.length, (index) {
                      final isSelected = index == navigationShell.currentIndex;
                      return GestureDetector(
                        onTap: () => ref.read(bottomNavProvider.notifier).onTabSelected(index),
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 7, vertical: 5),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              SvgPicture.asset(
                                iconAssets[index],
                                width: 100.w,
                                height: 100.h,
                              ),
                              SizedBox(height: 6),
                              AnimatedContainer(
                                duration: Duration(milliseconds: 200),
                                width: isSelected ? 15.w : 0,
                                height: isSelected ? 15.h : 0,
                                decoration: BoxDecoration(
                                  color: AppColors.primaryColor,
                                  shape: BoxShape.circle,
                                ),
                              ),
                            ],
                          ),
                        ),
                      );
                    }),
                  ),
                ),

              ),
            ),
          ),
        ),
      ),
    );
  }
}
