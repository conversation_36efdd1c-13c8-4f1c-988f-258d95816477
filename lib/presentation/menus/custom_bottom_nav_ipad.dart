import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../app/assets_manager.dart';
import '../../app/imports/packages_imports.dart';
import '../../core/providers/menus/bottom_nav_notifier.dart';
import '../../core/utils/Utils.dart';
import '../../core/widgets/custom_text.dart';

class CustomBottomNavIpad extends HookConsumerWidget {
  final StatefulNavigationShell navigationShell;
  final Color selectedColor;
  final Color unselectedColor;
  final Color backgroundColor;

  const CustomBottomNavIpad({
    super.key,
    required this.navigationShell,
    this.selectedColor = const Color(0xFFE01010),
    this.unselectedColor = const Color(0xFF747474),
    this.backgroundColor = Colors.white,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentIndex = ref.watch(bottomNavProvider);
    final bottomNavNotifier = ref.read(bottomNavProvider.notifier);

    // Set navigationShell ONCE
    useEffect(() {
      bottomNavNotifier.setNavigationShell(navigationShell);
      return null;
    }, []);

    final List<String> iconAssets = const [
      AssetsManager.ic_home,
      AssetsManager.ic_cookbooks,
      AssetsManager.ic_shop,
      AssetsManager.ic_profile,
    ];

    final List<String> labels = const [
      'Home',
      'Cookbooks',
      'Shop List',
      'Profile',
    ];

    return Scaffold(
      resizeToAvoidBottomInset: false,
      backgroundColor: Colors.transparent,
      body: GestureDetector(
        onTap: () => Utils().hideKeyboardOnTap(),
        behavior: HitTestBehavior.opaque,
        child: navigationShell,
      ),
      bottomNavigationBar: Container(
        height: 85,
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(16), topRight: Radius.circular(16)),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.2),
              spreadRadius: 2,
              blurRadius: 5,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: List.generate(iconAssets.length, (index) {
            return _BottomNavItem(
              iconAsset: iconAssets[index],
              label: labels[index],
              isSelected: currentIndex == index,
              selectedColor: selectedColor,
              unselectedColor: unselectedColor,
              onTap: () => bottomNavNotifier.onTabSelected(index),
            );
          }),
        ),
      ),
    );
  }
}

class _BottomNavItem extends StatelessWidget {
  final String iconAsset;
  final String label;
  final bool isSelected;
  final Color selectedColor;
  final Color unselectedColor;
  final VoidCallback onTap;

  const _BottomNavItem({
    required this.iconAsset,
    required this.label,
    required this.isSelected,
    required this.selectedColor,
    required this.unselectedColor,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOutCubicEmphasized,
        padding: EdgeInsets.symmetric(vertical: 8.h, horizontal: 16.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOutCubicEmphasized,
              width: isSelected ? 30 : 25,
              height: isSelected ? 30 : 25,
              child: SvgPicture.asset(
                iconAsset,
                color: isSelected ? selectedColor : unselectedColor,
              ),
            ),
            SizedBox(height: 4.h),
            AnimatedDefaultTextStyle(
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOutCubicEmphasized,
              style: TextStyle(
                color: isSelected ? selectedColor : unselectedColor,
                fontSize: isSelected ? 12 : 10,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
              ),
              child: CustomText(
                text: label,
                color: isSelected ? selectedColor : unselectedColor,
                size: isSelected ? 12 : 10,
                weight: isSelected ? FontWeight.w600 : FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
