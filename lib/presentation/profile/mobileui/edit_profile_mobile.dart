import 'dart:io';
import 'package:country_pickers/utils/utils.dart';
import 'package:country_pickers/country.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'package:mastercookai/app/assets_manager.dart';
import 'package:mastercookai/core/data/models/user_profile_response.dart';
import 'package:mastercookai/core/data/models/user_types_response.dart';
import 'package:mastercookai/core/data/request_query/update_profile_request.dart';
import 'package:mastercookai/core/network/app_status.dart';
import 'package:mastercookai/core/providers/profile/user_profile_notifier.dart';
import 'package:mastercookai/core/providers/profile/user_types_notifier.dart';
import 'package:mastercookai/core/utils/Utils.dart';
import 'package:mastercookai/core/utils/routing/routing_paths.dart';
import 'package:mastercookai/core/utils/validator.dart';
import 'package:mastercookai/core/widgets/contact_text_field.dart';
import 'package:mastercookai/core/widgets/custom_button.dart';
import 'package:mastercookai/core/widgets/custom_input_field.dart';
import 'package:mastercookai/core/widgets/custom_text.dart';
import 'package:mastercookai/core/widgets/image_cropper/image_cropper.dart';
import 'package:mastercookai/core/widgets/image_cropper/image_cropper_mobile.dart';
import 'package:mastercookai/core/widgets/simple_dropdown.dart';
import 'package:mastercookai/core/helpers/media_picker_service.dart';
import 'package:mastercookai/core/widgets/custom_appbar.dart';
import 'package:go_router/go_router.dart';
import '../../../app/theme/colors.dart';
import '../../../core/utils/device_utils.dart';
import 'sub_views/profile_picture_section.dart';
import 'sub_views/personal_details_section.dart';
import 'sub_views/company_details_section.dart';

class EditProfileMobile extends ConsumerStatefulWidget {
  const EditProfileMobile({super.key});

  @override
  ConsumerState<EditProfileMobile> createState() =>
      _MyAccountMobileSettingsState();
}

class _MyAccountMobileSettingsState extends ConsumerState<EditProfileMobile> {
  final TextEditingController firstNameController = TextEditingController();
  final TextEditingController lastNameController = TextEditingController();
  final TextEditingController emailController = TextEditingController();
  final TextEditingController contactController = TextEditingController();
  final TextEditingController companyNameController = TextEditingController();
  final TextEditingController typesOfUsersController = TextEditingController();
  final TextEditingController dateOfBirthController = TextEditingController();

  // State variables
  int selectedGender = 0; // 0 for Male, 1 for Female
  String? selectedUserType;
  int? selectedUserTypeId;
  File? selectedProfileImage;
  String? selectedDay;
  String? selectedMonth;
  String? selectedYear;
  Country? selectedCountry;
  bool _isFieldsPopulated = false;

  final _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    selectedCountry = CountryPickerUtils.getCountryByIsoCode('US');

    // Fetch user profile and user types data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(userProfileNotifierProvider.notifier).fetchUserProfile();
      ref.read(userTypesNotifierProvider.notifier).fetchUserTypes();

      final userProfileState = ref.read(userProfileNotifierProvider);
      if (userProfileState.data?.userProfile != null) {
        _populateFormFields(userProfileState.data!.userProfile!);
      }
    });
  }

  @override
  void dispose() {
    // Dispose controllers to prevent memory leaks
    firstNameController.dispose();
    lastNameController.dispose();
    emailController.dispose();
    contactController.dispose();
    companyNameController.dispose();
    typesOfUsersController.dispose();
    dateOfBirthController.dispose();
    super.dispose();
  }

  // Helper method to detect country from phone number
  void _detectCountryFromPhone(String phoneNumber) {
    final cleanPhone = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');
    setState(() {
      if (cleanPhone.startsWith('1') && cleanPhone.length == 11) {
        selectedCountry = CountryPickerUtils.getCountryByIsoCode('US');
      } else if (cleanPhone.startsWith('44')) {
        selectedCountry = CountryPickerUtils.getCountryByIsoCode('GB');
      } else if (cleanPhone.startsWith('91')) {
        selectedCountry = CountryPickerUtils.getCountryByIsoCode('IN');
      } else if (cleanPhone.length == 10) {
        selectedCountry = CountryPickerUtils.getCountryByIsoCode('US');
      } else {
        selectedCountry = CountryPickerUtils.getCountryByIsoCode('US');
      }
    });
  }

  // Helper method to populate form fields with user profile data
  void _populateFormFields(UserProfile userProfile) {
    final nameParts = userProfile.name.split(' ');
    final firstName = nameParts.isNotEmpty ? nameParts.first : '';
    final lastName = nameParts.length > 1 ? nameParts.sublist(1).join(' ') : '';

    firstNameController.text = firstName;
    lastNameController.text = lastName;
    emailController.text = userProfile.email;
    contactController.text = userProfile.phone;
    companyNameController.text = userProfile.companyName;
    typesOfUsersController.text = userProfile.userType;
    selectedUserType = userProfile.userType;
    selectedUserTypeId = userProfile.userTypeId;

    // Parse DOB
    final dobComponents = Utils().extractDobComponents(userProfile.dob);
    if (dobComponents != null) {
      setState(() {
        selectedDay = dobComponents['day']?.toString().padLeft(2, '0');
        selectedMonth = dobComponents['month'] as String?;
        selectedYear = dobComponents['year']?.toString();
        dateOfBirthController.text =
            userProfile.dob; // Keep in YYYY-MM-DD format
      });
    }

    _detectCountryFromPhone(userProfile.phone);

    setState(() {
      selectedGender = userProfile.gender.toLowerCase() == 'male' ? 0 : 1;
      _isFieldsPopulated = true;
    });
  }

  // Method to update profile with personal details and image
  Future<void> _updateProfile() async {
    if (!_formKey.currentState!.validate()) return;

    try {
      // Validate required fields
      if (firstNameController.text.trim().isEmpty) {
        Utils().showFlushbar(context,
            message: 'Please enter your first name', isError: true);
        return;
      }
      if (contactController.text.trim().isEmpty) {
        Utils().showFlushbar(context,
            message: 'Please enter your contact number', isError: true);
        return;
      }
      if (dateOfBirthController.text.trim().isEmpty) {
        Utils().showFlushbar(context,
            message: 'Please enter your date of birth', isError: true);
        return;
      }
      if (selectedUserTypeId == null) {
        Utils().showFlushbar(context,
            message: 'Please select a user type', isError: true);
        return;
      }

      // Create update profile request
      final request = UpdateProfileRequest(
        firstName: firstNameController.text.trim(),
        lastName: lastNameController.text.trim().isNotEmpty
            ? lastNameController.text.trim()
            : null,
        gender: selectedGender == 0 ? 'Male' : 'Female',
        dob: dateOfBirthController.text.trim(),
        countryCode: '+${selectedCountry?.phoneCode ?? '1'}',
        contact: contactController.text.trim(),
        companyName: companyNameController.text.trim().isNotEmpty
            ? companyNameController.text.trim()
            : null,
        userTypeId: selectedUserTypeId!,
        profilePic: selectedProfileImage,
      );

      // Call update profile API
      final success = await ref
          .read(userProfileNotifierProvider.notifier)
          .updateProfile(context: context, request: request);

      if (success && context.mounted) {
        // Fetch updated profile data
        await ref.read(userProfileNotifierProvider.notifier).fetchUserProfile();
        // Clear selected image
        setState(() {
          selectedProfileImage = null;
        });
      }
    } catch (e) {
      if (context.mounted) {
        Utils().showFlushbar(context,
            message: 'Failed to update profile: $e', isError: true);
      }
    }
  }

  // Method to pick profile image
  void _pickProfileImage() async {
    // Show dialog to choose between Camera and Gallery
    final source = await _showImageSourceDialog();
    if (source == null) return;

    final file = await _pickImageFromSource(source);
    if (file != null && mounted) {
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => ImageCropperMobile(
            pickedImage: file,
            showCropPresets: true,
            showGridLines: true,
            useDelegate: true,
            enableFreeformCrop: true,
            onImageCropped: (File? croppedImageFile) {
              if (croppedImageFile != null) {
                setState(() {
                  selectedProfileImage = croppedImageFile;
                });
              }
            },
          ),
        ),
      );
    }
  }

  Future<ImageSource?> _showImageSourceDialog() async {
    return showDialog<ImageSource>(
      context: context,
      builder: (context) => AlertDialog(
        title:
        CustomText(
          text:  'Select Image From',
          color: AppColors.blackTextColor,
          size: DeviceUtils().isTabletOrIpad(context) ? 24 : 24,
          weight: FontWeight.w700,
          align:TextAlign.center ,
        ),

        actions: [
          Column(
            children: [
              TextButton(
                onPressed: () => Navigator.pop(context, ImageSource.camera),
                child: Row(
                  children: [
                    const Icon(Icons.camera_alt,color: Colors.red,size: 24,),
                    const SizedBox(width: 8),
                    CustomText(
                      text:  'Camera',
                      color: AppColors.blackTextColor,
                      size: DeviceUtils().isTabletOrIpad(context) ? 14 : 14,
                      weight: FontWeight.w500,
                      align:TextAlign.center ,
                    ),
                  ],
                ),
              ),
              TextButton(
                onPressed: () => Navigator.pop(context, ImageSource.gallery),
                child: Row(
                  children: [
                    const Icon(Icons.photo_library,color: Colors.red,size: 24),
                    const SizedBox(width: 8),
                    CustomText(
                      text:  'Gallery',
                      color: AppColors.blackTextColor,
                      size: DeviceUtils().isTabletOrIpad(context) ? 14 : 14,
                      weight: FontWeight.w500,
                      align:TextAlign.center ,
                    ),
                  ],
                ),
              ),
              TextButton(
                onPressed: () => Navigator.pop(context, null),
                child:
                CustomText(
                  text:  'Cancel',
                  color: AppColors.primaryColor,
                  size: DeviceUtils().isTabletOrIpad(context) ? 14 : 14,
                  weight: FontWeight.w600,
                  align:TextAlign.end ,
                ),
              ),
            ],


          ),

        ],
      ),
    );
  }

  Future<File?> _pickImageFromSource(ImageSource source) async {
    try {
      final picker = ImagePicker();
      final XFile? xfile = await picker.pickImage(
        source: source,
        imageQuality: 80,
        maxWidth: 1024,
        maxHeight: 1024,
      );

      if (xfile == null) return null;
      return File(xfile.path);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error picking image: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
      return null;
    }
  }

  // Helper method to update the date controller when dropdowns change
  void _updateDateController() {
    if (selectedDay != null && selectedMonth != null && selectedYear != null) {
      // Convert short month name to number
      const monthNames = {
        'Jan': '01',
        'Feb': '02',
        'Mar': '03',
        'Apr': '04',
        'May': '05',
        'Jun': '06',
        'Jul': '07',
        'Aug': '08',
        'Sep': '09',
        'Oct': '10',
        'Nov': '11',
        'Dec': '12',
      };
      final monthNumber = monthNames[selectedMonth] ?? '01';
      // Format as YYYY-MM-DD for backend compatibility
      dateOfBirthController.text =
          '$selectedYear-$monthNumber-${selectedDay!.padLeft(2, '0')}';
    }
  }

  @override
  Widget build(BuildContext context) {
    final userProfileState = ref.watch(userProfileNotifierProvider);

    // Populate form fields when state changes to success
    if (!_isFieldsPopulated &&
        userProfileState.status == AppStatus.success &&
        userProfileState.data?.userProfile != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _populateFormFields(userProfileState.data!.userProfile!);
      });
    }
    return Scaffold(
      backgroundColor: AppColors.whiteColor,
      appBar: CustomAppBar(
        title: 'Edit Profile',
        onPressed: () {
          if (Navigator.of(context).canPop()) {
            Navigator.of(context).pop();
          } else {
            // Go back to My Profile page instead of home
            context.go(Routes.myAccountMobile);
          }
        },
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Profile Picture Section
              ProfilePictureSection(
                selectedProfileImage: selectedProfileImage,
                onPickImage: _pickProfileImage,
              ),
              const SizedBox(height: 24),

              // Personal Details Section
              PersonalDetailsSection(
                firstNameController: firstNameController,
                lastNameController: lastNameController,
                emailController: emailController,
                contactController: contactController,
                selectedGender: selectedGender,
                selectedDay: selectedDay,
                selectedMonth: selectedMonth,
                selectedYear: selectedYear,
                selectedCountry: selectedCountry,
                onGenderChanged: (value) {
                  setState(() {
                    selectedGender = value;
                  });
                },
                onDayChanged: (value) {
                  setState(() {
                    selectedDay = value;
                    _updateDateController();
                  });
                },
                onMonthChanged: (value) {
                  setState(() {
                    selectedMonth = value;
                    _updateDateController();
                  });
                },
                onYearChanged: (value) {
                  setState(() {
                    selectedYear = value;
                    _updateDateController();
                  });
                },
                onCountryChanged: (country) {
                  setState(() {
                    selectedCountry = country;
                  });
                },
              ),
              const SizedBox(height: 24),

              // Company Details Section
              CompanyDetailsSection(
                companyNameController: companyNameController,
                typesOfUsersController: typesOfUsersController,
                selectedUserType: selectedUserType,
                selectedUserTypeId: selectedUserTypeId,
                onUserTypeChanged: (type, id) {
                  setState(() {
                    selectedUserType = type;
                    selectedUserTypeId = id;
                  });
                },
              ),
              const SizedBox(height: 32),

              // Save Changes Button
              Center(
                child: CustomButton(
                  text: 'Save Changes',
                  onPressed: _updateProfile,
                  width: double.infinity,
                  height: 38,
                  fontSize: 15,
                  fontFamily: 'Inter',
                  borderRadius: 10,
                ),
              ),
              const SizedBox(height: 40),
            ],
          ),
        ),
      ),
    );
  }
}
