import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import '../../../../app/assets_manager.dart';
import '../../../../app/imports/packages_imports.dart';
import '../../../../app/theme/colors.dart';
import '../../../../core/data/models/user_types_response.dart';
import '../../../../core/network/app_status.dart';
import '../../../../core/providers/profile/user_types_notifier.dart';
import '../../../../core/widgets/custom_text.dart';

class UserTypesDropdownField extends ConsumerStatefulWidget {
  final String? selectedUserType;
  final Function(UserType)? onChanged;

  const UserTypesDropdownField({
    super.key,
    this.selectedUserType,
    required this.onChanged,
  });

  @override
  UserTypesDropdownFieldState createState() => UserTypesDropdownFieldState();
}

class UserTypesDropdownFieldState extends ConsumerState<UserTypesDropdownField> {
  String? _selectedUserType;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(userTypesNotifierProvider.notifier).fetchUserTypes();
    });
    _selectedUserType = widget.selectedUserType;
  }

  @override
  Widget build(BuildContext context) {
    final userTypesState = ref.watch(userTypesNotifierProvider);

    print("UserTypesState: ${userTypesState.data?.userTypes} ${userTypesState.status}");

    if (userTypesState.status == AppStatus.success && userTypesState.data != null) {
      final uniqueUserTypes = userTypesState.data!.userTypes.toSet().toList();

      return Container(
        height: 40,
        padding: const EdgeInsets.symmetric(horizontal: 12),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade300),
          borderRadius: BorderRadius.circular(8),
        ),
        child: DropdownButtonHideUnderline(
          child: DropdownButton<UserType>(
            value: _selectedUserType != null
                ? uniqueUserTypes.firstWhere(
                  (type) => type.type == _selectedUserType,
              orElse: () => uniqueUserTypes.isNotEmpty
                  ? uniqueUserTypes.first
                  : UserType(id: 0, type: ''),
            )
                : null,
            hint: Text(
              'Select User Type',
              style: TextStyle(
                fontSize: 14,
                color: AppColors.primaryLightTextColor,
                fontFamily: "Inter-Regular",
                fontWeight: FontWeight.w400,
              ),
            ),
            isExpanded: true,
            items: uniqueUserTypes.map((userType) {
              return DropdownMenuItem<UserType>(
                value: userType,
                child: Text(
                  userType.type,
                  style: TextStyle(
                    fontSize: 14,
                    color: AppColors.primaryLightTextColor,
                    fontFamily: "Inter-Regular",
                    fontWeight: FontWeight.w400,
                  ),
                ),
              );
            }).toList(),
            onChanged: (UserType? value) {
              setState(() {
                _selectedUserType = value?.type;
              });
              if (value != null && widget.onChanged != null) {
                widget.onChanged!(value);
              }
            },
            icon: Container(
              padding: const EdgeInsets.all(4),
              decoration: BoxDecoration(
                color: AppColors.primaryColor,
                borderRadius: BorderRadius.circular(4),
              ),
              child: SvgPicture.asset(
                AssetsManager.droparrow,
                height: 14,
                width: 10,
              ),
            ),
          ),
        ),
      );
    }  else {
      return Container(
        height: 40,
        decoration: BoxDecoration(
          color: Colors.grey[100],
          borderRadius: BorderRadius.circular(8),
        ),
        child: const Center(
          child: CustomText(
            text: 'Loading User Type...',
            size: 14,
            weight: FontWeight.w400,
            color: Colors.grey,
          ),
        ),
      );
    }
  }
}