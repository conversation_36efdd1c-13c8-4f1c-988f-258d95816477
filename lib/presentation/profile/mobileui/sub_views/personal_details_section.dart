import 'package:flutter/material.dart';
import 'package:country_pickers/country.dart';
import 'package:mastercookai/app/theme/colors.dart';
import 'package:mastercookai/core/utils/validator.dart';
import 'package:mastercookai/core/widgets/contact_text_field.dart';
import 'package:mastercookai/core/widgets/custom_input_field.dart';
import 'package:mastercookai/core/widgets/custom_text.dart';
import 'package:mastercookai/core/widgets/simple_dropdown.dart';

class PersonalDetailsSection extends StatefulWidget {
  final TextEditingController firstNameController;
  final TextEditingController lastNameController;
  final TextEditingController emailController;
  final TextEditingController contactController;
  final int selectedGender;
  final String? selectedDay;
  final String? selectedMonth;
  final String? selectedYear;
  final Country? selectedCountry;
  final Function(int) onGenderChanged;
  final Function(String?) onDayChanged;
  final Function(String?) onMonthChanged;
  final Function(String?) onYearChanged;
  final Function(Country) onCountryChanged;

  const PersonalDetailsSection({
    super.key,
    required this.firstNameController,
    required this.lastNameController,
    required this.emailController,
    required this.contactController,
    required this.selectedGender,
    required this.selectedDay,
    required this.selectedMonth,
    required this.selectedYear,
    required this.selectedCountry,
    required this.onGenderChanged,
    required this.onDayChanged,
    required this.onMonthChanged,
    required this.onYearChanged,
    required this.onCountryChanged,
  });

  @override
  State<PersonalDetailsSection> createState() => _PersonalDetailsSectionState();
}

class _PersonalDetailsSectionState extends State<PersonalDetailsSection> {
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        CustomText(
          text: 'Personal Details:',
          size: 15,
          weight: FontWeight.w500,
          color: AppColors.primaryLightTextColor,
        ),
        const SizedBox(height: 10),

        // Gender Selection
        _buildLabeledField('Gender', _buildGenderRadioButtons()),
        const SizedBox(height: 20),

        // First Name
        _buildLabeledField(
          'First Name',
          SizedBox(
            child: SizedBox(
              child: CustomInputField(
                hintText: '',
                controller: widget.firstNameController,
                validator: Validator.validateFName,
                isUpdateMode: true,
                borderRadius: 6,
                maxLength: 50,
                fontSize: 14,
              ),
            ),
          ),
        ),
        const SizedBox(height: 20),

        // Last Name
        _buildLabeledField(
          'Last Name',
          CustomInputField(
            hintText: '',
            controller: widget.lastNameController,
            validator: Validator.validateLName,
            isUpdateMode: true,
            borderRadius: 6,
            maxLength: 50,
            fontSize: 14,
          ),
        ),
        const SizedBox(height: 20),

        // Date of Birth
        _buildLabeledField('Date of birth', _buildDateDropdowns()),
        const SizedBox(height: 20),

        // Email
        _buildLabeledField(
          'Email',
          CustomInputField(
            hintText: '<EMAIL>',
            controller: widget.emailController,
            validator: Validator.validateEmail,
            editable: false,
            isUpdateMode: true,
            borderRadius: 6,
            fontSize: 13,
          ),
        ),

        const SizedBox(height: 20),

        // Contact
        _buildLabeledField(
          'Contact',
          SizedBox()
          // ContactTextField(
          //           controller: widget.contactController,
          //           selectedCountry: widget.selectedCountry!,
          //           onCountryChanged: widget.onCountryChanged,
          //           validator: Validator.validatePhoneNumber,
          //           isUpdateMode: true,
          //           borderRadius: 6,
          //           fontSize: 13,
          //           onClear: () {
          //             widget.contactController.clear();
          //           },
          //         ),
        ),
      ],
    );
  }

  // Helper method to create labeled fields
  Widget _buildLabeledField(String label, Widget field) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          flex: 1,

          child: Padding(
            padding: const EdgeInsets.only(top: 5),
            child: CustomText(
              text: label,
              size: 13,
              weight: FontWeight.w400,
              color: AppColors.blackColor,
            ),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
            flex: 2,
            child: field),
      ],
    );
  }

  // Gender Radio Buttons
  Widget _buildGenderRadioButtons() {
    return Row(
      children: [
        Expanded(
          child: GestureDetector(
            onTap: () => widget.onGenderChanged(0),
            child: Row(
              children: [
                Radio<int>(
                  value: 0,
                  groupValue: widget.selectedGender,
                  activeColor: AppColors.primaryColor,
                  materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  visualDensity: VisualDensity.compact,
                  onChanged: (int? value) {
                    if (value != null) widget.onGenderChanged(value);
                  },
                ),
                const SizedBox(width: 4),
                CustomText(
                  text: 'Male',
                  size: 14,
                  weight: FontWeight.w400,
                  color: AppColors.blackColor,
                ),
              ],
            ),
          ),
        ),
        Expanded(
          child: GestureDetector(
            onTap: () => widget.onGenderChanged(1),
            child: Row(
              children: [
                Radio<int>(
                  value: 1,
                  groupValue: widget.selectedGender,
                  activeColor: AppColors.primaryColor,
                  materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  visualDensity: VisualDensity.compact,
                  onChanged: (int? value) {
                    if (value != null) widget.onGenderChanged(value);
                  },
                ),
                const SizedBox(width: 4),
                CustomText(
                  text: 'Female',
                  size: 14,
                  weight: FontWeight.w400,
                  color: AppColors.blackColor,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  // Date Dropdowns
  Widget _buildDateDropdowns() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(),
        Row(
          children: [
            // Day Dropdown
            Expanded(
              child: SimpleDropdown<String>(
                items: List.generate(
                    31, (index) => (index + 1).toString().padLeft(2, '0')),
                hintText: '0',
                selectedValue: widget.selectedDay,
                displayText: (day) => day,
                onChanged: widget.onDayChanged,
                validator: (value) => value == null ? 'Select Day' : null,
                height: 40,
                borderRadius: 8,
                fontSize: 13,
              ),
            ),
            const SizedBox(width: 8),
            // Month Dropdown
            Expanded(
              child: SimpleDropdown<String>(
                items: [
                  'Jan',
                  'Feb',
                  'Mar',
                  'Apr',
                  'May',
                  'Jun',
                  'Jul',
                  'Aug',
                  'Sep',
                  'Oct',
                  'Nov',
                  'Dec'
                ],
                hintText: 'Mar',
                selectedValue: widget.selectedMonth,
                displayText: (month) => month,
                onChanged: widget.onMonthChanged,
                validator: (value) => value == null ? 'Select Month' : null,
                height: 40,
                borderRadius: 8,
                fontSize: 13,
              ),
            ),
            const SizedBox(width: 8),
            // Year Dropdown
            Expanded(
              child: SimpleDropdown<String>(
                items: List.generate(
                    100, (index) => (DateTime.now().year - index).toString()),
                hintText: '1982',
                selectedValue: widget.selectedYear,
                displayText: (year) => year,
                onChanged: widget.onYearChanged,
                validator: (value) => value == null ? 'Select Year' : null,
                height: 40,
                borderRadius: 8,
                fontSize: 13,
              ),
            ),
          ],
        ),
      ],
    );
  }
}
