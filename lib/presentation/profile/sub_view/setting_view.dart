import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mastercookai/core/network/app_status.dart';
import 'package:mastercookai/core/providers/profile/user_profile_notifier.dart';
import 'package:mastercookai/core/utils/Utils.dart';
import 'package:mastercookai/core/utils/device_utils.dart';
import 'package:mastercookai/core/utils/validator.dart';
import 'package:mastercookai/core/widgets/custom_input_field.dart';
import 'package:mastercookai/core/widgets/custom_text.dart';

class SettingView extends ConsumerStatefulWidget {
  const SettingView({super.key});

  @override
  ConsumerState<SettingView> createState() => _SettingViewState();
}

class _SettingViewState extends ConsumerState<SettingView> {
  // Password controllers
  final TextEditingController _currentPasswordController =
      TextEditingController();
  final TextEditingController _newPasswordController = TextEditingController();
  final TextEditingController _confirmNewPasswordController =
      TextEditingController();

  // State for checkbox (newsletter)
  bool _subscribeNewsletter = true;

  // State for password visibility
  bool _isCurrentPasswordVisible = false;
  bool _isNewPasswordVisible = false;
  bool _isConfirmPasswordVisible = false;

  // State for password validation
  bool _showPasswordValidation = false;

  @override
  void initState() {
    super.initState();
    // Add listeners for real-time password validation
    _newPasswordController.addListener(_updatePasswordValidation);
    _confirmNewPasswordController.addListener(_updatePasswordValidation);
  }

  @override
  void dispose() {
    _currentPasswordController.dispose();
    _newPasswordController.dispose();
    _confirmNewPasswordController.dispose();
    super.dispose();
  }

  void _updatePasswordValidation() {
    setState(() {
      _showPasswordValidation = _newPasswordController.text.isNotEmpty &&
          _confirmNewPasswordController.text.isNotEmpty;
    });
  }

  bool _arePasswordsMatching() {
    return _newPasswordController.text == _confirmNewPasswordController.text;
  }

  String? _validatePassword(String password) {
    // Use your existing Validator class or define custom validation
    return Validator.validatePassword(password);
  }

  Future<void> _handleSavePassword(WidgetRef ref) async {
    if (_currentPasswordController.text.trim().isEmpty) {
      Utils().showSnackBar(context, 'Please enter your current password');
      return;
    }
    if (_newPasswordController.text.trim().isEmpty) {
      Utils().showSnackBar(context, 'Please enter a new password');
      return;
    }
    if (_confirmNewPasswordController.text.trim().isEmpty) {
      Utils().showSnackBar(context, 'Please confirm your new password');
      return;
    }

    // Validate new password format
    final passwordError = _validatePassword(_newPasswordController.text.trim());
    if (passwordError != null) {
      Utils().showSnackBar(context, passwordError);
      return;
    }

    // Check if passwords match
    if (!_arePasswordsMatching()) {
      Utils().showSnackBar(context, 'New passwords do not match');
      return;
    }

    try {
      // Call reset password API
      await ref.read(userProfileNotifierProvider.notifier).resetPassword(
        context,
        _currentPasswordController.text.trim(),
        _newPasswordController.text.trim(),
        _confirmNewPasswordController.text.trim(),
        onSuccess: () {
          // Clear fields on success
          _currentPasswordController.clear();
          _newPasswordController.clear();
          _confirmNewPasswordController.clear();
        },
      );

      // Check the final state after API call
      final userProfileState = ref.read(userProfileNotifierProvider);
      if (userProfileState.status == AppStatus.success && context.mounted) {
        // Clear fields again to ensure UI consistency
        _currentPasswordController.clear();
        _newPasswordController.clear();
        _confirmNewPasswordController.clear();
      }
    } catch (e) {
      if (context.mounted) {
        Utils().showSnackBar(context, 'Failed to update password: $e');
      }
    }
  }

  Future<void> _handleDeleteAccount(WidgetRef ref) async {
    final bool? shouldDelete = await Utils().showCommonConfirmDialog(
      context: context,
      title: 'Delete Account',
      subtitle: 'Are you sure you want to delete your account?',
      confirmText: 'Delete',
      cancelText: 'Cancel',
    );

    if (shouldDelete == true && context.mounted) {
      try {
        await ref
            .read(userProfileNotifierProvider.notifier)
            .deleteAccount(context);
      } catch (e) {
        Utils().showSnackBar(context, 'Error deleting account: $e');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.only(
          left: 40.0, right: 40.0, top: 30.0, bottom: 30.0),
      child: Column(
        children: [
          Align(
            alignment: DeviceUtils().isTabletOrIpad(context)
                ? Alignment.center
                : Alignment.centerLeft,
            child: Container(
              width: 600,
              padding: const EdgeInsets.all(50),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(15),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withValues(alpha: 0.15),
                    spreadRadius: 2,
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Change Password Section
                  CustomText(
                    text: 'Change Password:',
                    size: 14,
                    weight: FontWeight.w600,
                    color: Colors.grey[700],
                  ),
                  const SizedBox(height: 24),
                  // Current Password Field
                  _buildExactSettingsField(
                    'Current Password',
                    _currentPasswordController,
                    obscureText: true,
                    showValue: '********',
                    isPasswordVisible: _isCurrentPasswordVisible,
                    onToggleVisibility: () => setState(() =>
                        _isCurrentPasswordVisible = !_isCurrentPasswordVisible),
                  ),
                  const SizedBox(height: 18),
                  // New Password Field
                  _buildExactSettingsField(
                    'New Password',
                    _newPasswordController,
                    obscureText: true,
                    isPasswordVisible: _isNewPasswordVisible,
                    onToggleVisibility: () => setState(
                        () => _isNewPasswordVisible = !_isNewPasswordVisible),
                  ),
                  const SizedBox(height: 18),
                  // Confirm New Password Field
                  _buildExactSettingsField(
                    'Confirm New Password',
                    _confirmNewPasswordController,
                    obscureText: true,
                    isPasswordVisible: _isConfirmPasswordVisible,
                    onToggleVisibility: () => setState(() =>
                        _isConfirmPasswordVisible = !_isConfirmPasswordVisible),
                  ),
                  const SizedBox(height: 10),
                  // Password validation messages
                  if (_showPasswordValidation)
                    Padding(
                      padding: const EdgeInsets.only(left: 220, bottom: 10),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // New password validation
                          if (_newPasswordController.text.isNotEmpty)
                            Builder(
                              builder: (context) {
                                final error = _validatePassword(
                                    _newPasswordController.text);
                                if (error != null) {
                                  return Padding(
                                    padding: const EdgeInsets.only(bottom: 5),
                                    child: CustomText(
                                      text: error,
                                      size:
                                          DeviceUtils().isTabletOrIpad(context)
                                              ? 12
                                              : 16.sp,
                                      color: Theme.of(context).primaryColor,
                                      weight: FontWeight.w500,
                                    ),
                                  );
                                }
                                return const SizedBox.shrink();
                              },
                            ),
                          // Password match validation
                          if (_newPasswordController.text.isNotEmpty &&
                              _confirmNewPasswordController.text.isNotEmpty)
                            CustomText(
                              text: _arePasswordsMatching()
                                  ? 'Passwords match ✓'
                                  : 'Passwords do not match ✗',
                              size: DeviceUtils().isTabletOrIpad(context)
                                  ? 12
                                  : 16.sp,
                              color: _arePasswordsMatching()
                                  ? Colors.green[600]
                                  : Theme.of(context).primaryColor,
                              weight: FontWeight.w500,
                            ),
                        ],
                      ),
                    ),
                  const SizedBox(height: 10),
                  // Save Password Button
                  Consumer(
                    builder: (context, ref, child) {
                      final userProfileState =
                          ref.watch(userProfileNotifierProvider);
                      final isLoading =
                          userProfileState.status == AppStatus.updating;

                      return SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          onPressed:
                              isLoading ? null : () => _handleSavePassword(ref),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Theme.of(context).primaryColor,
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(6),
                            ),
                            elevation: 0,
                          ),
                          child: isLoading
                              ? const SizedBox(
                                  width: 20,
                                  height: 20,
                                  child: CircularProgressIndicator(
                                    color: Colors.white,
                                    strokeWidth: 2,
                                  ),
                                )
                              : CustomText(
                                  text: 'Save Password',
                                  size: 16,
                                  color: Colors.white,
                                  weight: FontWeight.w500,
                                ),
                        ),
                      );
                    },
                  ),
                  const SizedBox(height: 30),
                  // Newsletter Section
                  CustomText(
                    text: 'Newsletter:',
                    size: 16,
                    weight: FontWeight.w600,
                    color: Colors.grey[700],
                  ),
                  const SizedBox(height: 20),
                  // Subscribe Newsletter Checkbox
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      GestureDetector(
                        onTap: () {
                          setState(() {
                            _subscribeNewsletter = !_subscribeNewsletter;
                          });
                        },
                        child: CustomText(
                          text: 'Subscribe Newsletter',
                          size: 14,
                          weight: FontWeight.w400,
                          color: Colors.grey[700],
                        ),
                      ),
                      GestureDetector(
                        onTap: () {
                          setState(() {
                            _subscribeNewsletter = !_subscribeNewsletter;
                          });
                        },
                        child: Container(
                          width: 20,
                          height: 20,
                          decoration: BoxDecoration(
                            color: _subscribeNewsletter
                                ? Theme.of(context).primaryColor
                                : Colors.white,
                            border: Border.all(
                              color: _subscribeNewsletter
                                  ? Theme.of(context).primaryColor
                                  : Colors.grey[400]!,
                              width: 2,
                            ),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: _subscribeNewsletter
                              ? const Icon(
                                  Icons.check,
                                  size: 14,
                                  color: Colors.white,
                                )
                              : null,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          // Remove Account Button
          const SizedBox(height: 30),
          Align(
            alignment: Alignment.centerLeft,
            child: SizedBox(
              width: 600,
              child: Center(
                child: Consumer(
                  builder: (context, ref, child) {
                    final userProfileState =
                        ref.watch(userProfileNotifierProvider);
                    final isDeleteLoading =
                        userProfileState.status == AppStatus.deleting;

                    return GestureDetector(
                      onTap: isDeleteLoading
                          ? null
                          : () => _handleDeleteAccount(ref),
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 40, vertical: 15),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: Colors.grey[100]!,
                            width: 1,
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.grey.withValues(alpha: 0.15),
                              spreadRadius: 2,
                              blurRadius: 10,
                              offset: const Offset(0, 4),
                            ),
                          ],
                        ),
                        child: isDeleteLoading
                            ? Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  SizedBox(
                                    width: 16,
                                    height: 16,
                                    child: CircularProgressIndicator(
                                      color: Theme.of(context).primaryColor,
                                      strokeWidth: 2,
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                  CustomText(
                                    text: 'Deleting...',
                                    size: 14,
                                    color: Theme.of(context).primaryColor,
                                    weight: FontWeight.w500,
                                  ),
                                ],
                              )
                            : CustomText(
                                text: 'Remove Account',
                                size: 14,
                                color: Theme.of(context).primaryColor,
                                weight: FontWeight.w500,
                              ),
                      ),
                    );
                  },
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Helper widget for settings input fields
  Widget _buildExactSettingsField(
    String label,
    TextEditingController controller, {
    bool obscureText = false,
    String? showValue,
    bool? isPasswordVisible,
    VoidCallback? onToggleVisibility,
  }) {
    return Row(
      children: [
        SizedBox(
          width: 200,
          child: CustomText(
            text: label,
            size: 12,
            color: Colors.grey[700],
            weight: FontWeight.w400,
          ),
        ),
        const SizedBox(width: 20),
        Expanded(
          child: CustomInputField(
            hintText: label,
            controller: controller,
            isPassword: true,
          ),
        ),
      ],
    );
  }
}
