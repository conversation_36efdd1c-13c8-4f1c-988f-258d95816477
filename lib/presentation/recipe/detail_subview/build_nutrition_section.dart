import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mastercookai/core/network/app_status.dart';
import 'package:mastercookai/core/providers/nutrions_notifier.dart';
import 'package:mastercookai/core/utils/device_utils.dart';
import 'package:mastercookai/presentation/recipe/detail_subview/recipe_nutrition_facts_dialog.dart';
import 'package:mastercookai/presentation/shimer/nutritions_shimmer.dart';
import '../../../app/imports/core_imports.dart';
import '../../../core/data/models/nutritionInfo_response.dart';
import '../../../core/data/models/recipe_detail_response.dart';
import '../../../core/widgets/custom_text.dart';
import '../../cookbook/widgets/custom_title_text.dart';
import 'build_nutrition_card.dart';

class NutritionSection extends ConsumerStatefulWidget {
  final RecipeDetails? recipeDetail;

  const NutritionSection({
    super.key,
    required this.recipeDetail,
  });

  @override
  ConsumerState<NutritionSection> createState() => _NutritionSectionState();
}

class _NutritionSectionState extends ConsumerState<NutritionSection> {
  @override
  Widget build(BuildContext context) {
    final nutritionDataState = ref.watch(nutritionInfoNotifierProvider);

    return switch (nutritionDataState.status) {
      AppStatus.loading => nutritionsShimmer(context),
      AppStatus.success => _buildNutritionContent(context, nutritionDataState),
      AppStatus.error => Text(''),
      // TODO: Handle this case.
      AppStatus.otpVerificationSuccess => throw UnimplementedError(),
      // TODO: Handle this case.
      AppStatus.idle => throw UnimplementedError(),
      // TODO: Handle this case.
      AppStatus.loadingMore => throw UnimplementedError(),
      // TODO: Handle this case.
      AppStatus.empty => throw UnimplementedError(),
      // TODO: Handle this case.
      AppStatus.creating => throw UnimplementedError(),
      // TODO: Handle this case.
      AppStatus.createSuccess => throw UnimplementedError(),
      // TODO: Handle this case.
      AppStatus.createError => throw UnimplementedError(),
      // TODO: Handle this case.
      AppStatus.updating => throw UnimplementedError(),
      // TODO: Handle this case.
      AppStatus.updateSuccess => throw UnimplementedError(),
      // TODO: Handle this case.
      AppStatus.updateError => throw UnimplementedError(),
      // TODO: Handle this case.
      AppStatus.deleting => throw UnimplementedError(),
      // TODO: Handle this case.
      AppStatus.deleteSuccess => throw UnimplementedError(),
      // TODO: Handle this case.
      AppStatus.deleteError => throw UnimplementedError(),
    };
  }

  Widget _buildNutritionContent(BuildContext context, AppState<NutritionInfoData> nutritionDataState) {
    final hasNutritionData = nutritionDataState.data?.nutrients?.isNotEmpty ?? false;

    return Visibility(
      visible: hasNutritionData,
      replacement: Center(
        child: Text(
          'No nutrition facts available',
          style: context.theme.textTheme.bodySmall!.copyWith(
            color: AppColors.blackColor,
            fontWeight: FontWeight.w400,
            fontSize: 14,
          ),
        ),
      ),
      child: Container(
        padding:   EdgeInsets.all(getDeviceType(context).name!='mobile'?16:0),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            if(getDeviceType(context).name!='mobile')
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Visibility(
                visible: getDeviceType(context).name=='mobile',
                child: SizedBox(height: 16,)),
            CustomText(
              text: 'Nutrition Analysis',
              size: 14,
              weight: FontWeight.w600,
              color: AppColors.primaryGreyColor,
            ),
             const SizedBox(height: 20),
            GridView.count(
              shrinkWrap: true,
              crossAxisCount: 2,
              crossAxisSpacing: 15,
              mainAxisSpacing: 10,
              physics: const NeverScrollableScrollPhysics(),
              childAspectRatio: 2,
              children: nutritionDataState.data?.nutrients?.map((fact) {
                return buildNutritionCard(
                  context,
                  fact.name ?? '',
                  fact.amount ?? '',
                );
              }).toList() ??
                  [],
            ),
            const SizedBox(height: 20),
            Center(
              child: TextButton(
                onPressed: () {
                  showDialog(
                    context: context,
                    builder: (_) => RecipeNutritionFactsDialog(
                      recipeDetails: widget.recipeDetail!,
                      nutritionData: nutritionDataState.data,
                    ),
                  );
                },
                child: Text(
                  'View detailed nutrition facts',
                  style: context.theme.textTheme.displaySmall!.copyWith(
                    color: AppColors.primaryBorderColor,
                    fontWeight: FontWeight.w400,
                    decoration: TextDecoration.underline,
                    decorationColor: AppColors.primaryBorderColor,
                    decorationThickness: 1.0,
                    fontSize: 12,
                  ),
                ),
              ),
            ),
            SizedBox(height: 30),
          ],
        ),
      ),
    );
  }
}
