import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:mastercookai/core/network/app_status.dart';
import 'package:mastercookai/core/utils/device_utils.dart';
import 'package:mastercookai/core/widgets/custom_text.dart';
import 'package:mastercookai/presentation/recipe/detail_subview/recipe_nutrition_facts_dialog.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../../../app/imports/core_imports.dart';
import '../../../core/data/models/recipe_detail_response.dart';
import '../../../core/providers/nutrions_notifier.dart';
import '../../../core/widgets/custom_doted_lines.dart';
import '../../../core/widgets/expandable_desc_text.dart';

class RecipeHeader extends ConsumerStatefulWidget {
  final RecipeDetails recipeDetail;

  const RecipeHeader({
    super.key,
    required this.recipeDetail,
  });

  @override
  ConsumerState<RecipeHeader> createState() => _RecipeHeaderState();
}

class _RecipeHeaderState extends ConsumerState<RecipeHeader> {
  @override
  Widget build(BuildContext context) {
    final nutritionDataState = ref.watch(nutritionInfoNotifierProvider);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Stack(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Flexible(
                  child: CustomText(
                  text:   widget.recipeDetail.name ?? '',
                    size: 30,
                    weight: FontWeight.w700,
                    color: AppColors.primaryGreyColor,
                        overflow: TextOverflow.ellipsis,
                  ),
                ),
                Visibility(
                  visible: false,
                  child: Row(
                    children: [
                      Container(
                        padding: EdgeInsets.symmetric(
                            horizontal: 12.w, vertical: 8.h),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(4),
                          border: Border.all(
                            color: AppColors.primaryGreyColor,
                            width: 1,
                          ),
                        ),
                        child: Row(
                          children: [
                            SvgPicture.asset(
                              AssetsManager.share,
                              width: 20.w,
                              height: 20.h,
                              color: AppColors.primaryBorderColor,
                            ),
                            SizedBox(width: 8.w),
                            Text(
                              'Share',
                              style: TextStyle(
                                fontSize: 12,
                                color: AppColors.primaryGreyColor,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                      SizedBox(width: 12.w),
                      Container(
                        padding: EdgeInsets.symmetric(
                            horizontal: 12.w, vertical: 8.h),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(4),
                          border: Border.all(
                            color: AppColors.primaryGreyColor,
                            width: 1,
                          ),
                        ),
                        child: Row(
                          children: [
                            SvgPicture.asset(
                              AssetsManager.print,
                              width: 20.w,
                              height: 20.h,
                              color: AppColors.primaryBorderColor,
                            ),
                            SizedBox(width: 8.w),
                            Text(
                              'Print',
                              style: TextStyle(
                                fontSize: 12,
                                color: AppColors.primaryGreyColor,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            Visibility(
              visible: nutritionDataState.status == AppStatus.success &&
                  nutritionDataState.data?.nutrients != null &&
                  DeviceUtils().isTabletOrIpad(context),
              child: Align(
                  alignment: Alignment.topRight,
                  child: GestureDetector(
                    onTap: () {
                      showDialog(
                        context: context,
                        builder: (BuildContext context) {
                          return RecipeNutritionFactsDialog(
                            recipeDetails: widget.recipeDetail,
                            nutritionData: nutritionDataState.data,
                          );
                        },
                      );
                    },
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(CupertinoIcons.heart_fill, size: 28),
                        CustomText(
                          text: 'Nutrition Facts',
                          weight: FontWeight.w500,
                          size: 14,
                          overflow: TextOverflow.ellipsis,
                        )
                      ],
                    ),
                  )),
            ),
          ],
        ),
        SizedBox(height: 20.h),
        CustomPaint(
          painter: DottedLinePainter(
            strokeWidth: 1,
            dashWidth: 6,
            color: AppColors.lightestGreyColor,
          ),
          size: Size(double.infinity, 2),
        ),
        SizedBox(height: 20.h),
        ExpandableDescText(
          desc: widget.recipeDetail.description ?? '',
          textColor: AppColors.primaryLightTextColor,
          size: 14,
        ),
      ],
    );
  }
}
