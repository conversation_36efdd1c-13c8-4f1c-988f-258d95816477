import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mastercookai/app/assets_manager.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/core/utils/device_utils.dart';
import 'package:mastercookai/core/widgets/custom_appbar.dart';
import 'package:mastercookai/presentation/recipe/subview/media_picker_grid.dart';
import 'package:mastercookai/presentation/recipe/subview/custom_directions_widegts.dart';
import 'package:mastercookai/presentation/recipe/subview/custom_serving_ideas_widget.dart';
import 'package:mastercookai/presentation/recipe/subview/custom_wine_widgets.dart';
import 'package:mastercookai/presentation/recipe/subview/recipe_cards.dart';
import 'package:mastercookai/presentation/recipe/tab/tab_edit_recipe.dart';
import '../../../app/imports/packages_imports.dart';
import '../../../core/widgets/custom_button.dart';
import '../../../core/widgets/custom_searchbar.dart';
import '../../../core/widgets/custom_drawer.dart';
import '../../core/data/models/category_response.dart';
import '../../core/data/models/cuisines_response.dart';
import '../../core/data/models/recipe_detail_response.dart';
import '../../core/data/models/recipe_response.dart';
import '../../core/data/request_query/create_recipe_request.dart';
import '../../core/data/request_query/recipe_meta_data.dart';
import '../../core/providers/recipe/directions_provider.dart';
import '../../core/providers/recipe/ingrident_provider.dart';
import '../../core/providers/recipe/media_provider.dart';
import '../../core/providers/recipe_notifier.dart';
import '../../core/providers/recipe/author_provider.dart';
import '../../core/providers/recipe/metadata_provider.dart';
import '../../core/providers/recipe/notes_provider.dart';
import '../../core/providers/single_recipe_notifier.dart';
import '../../core/utils/Utils.dart';
import 'edit_recipe_subview/buildAuthorSection.dart';
import 'edit_recipe_subview/buildIngredientsSection.dart';
import 'edit_recipe_subview/buildMainContent.dart';
import 'edit_recipe_subview/buildNotesSection.dart';
import 'edit_recipe_subview/buildNutritionSection.dart';
import 'edit_recipe_subview/buildRecipeHeader.dart';
import 'edit_recipe_subview/buildRecipeMetadata.dart';
import 'edit_recipe_subview/build_recipelist.dart';
import 'mobile_ui/edit_recipe_screen_mobile.dart';

class EditRecipeScreen extends ConsumerStatefulWidget {
  final int recipeId;
  final int cookbookId;
  List<Recipe> recipesList;
  final RecipeDetails recipeDetails;
  final List<Categories> categories;
  final List<Cuisines> cuisines;
  final int? initialStep;

  EditRecipeScreen({
    super.key,
    required this.recipeId,
    required this.cookbookId,
    required this.recipesList,
    required this.recipeDetails,
    required this.categories,
    required this.cuisines,
    this.initialStep,
  });

  @override
  ConsumerState<EditRecipeScreen> createState() => _EditRecipeScreenState();
}

class _EditRecipeScreenState extends ConsumerState<EditRecipeScreen> {
  final TextEditingController searchController = TextEditingController();
  int _selectedCookbookIndex = 0;
  late TextEditingController recipeNameController;
  late TextEditingController recipeDescController;
  late List<String> ingredients;
  late NutritionInfo nutritionFacts;
  late List<Directions> directions;
  late List<RecipeMedia> recipeMedia;
  late String servingIdeas;
  late String wine;
  late String author;
  late String authorMediaUrl;
  late String copyright;
  late String source;
  late String notes;
  String? _category;
  String? _cuisine;
  bool _isLoading = true;
  bool isUpdate = false;

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  void _initializeData() {
    recipeNameController =
        TextEditingController(text: widget.recipeDetails.name ?? '');
    recipeDescController =
        TextEditingController(text: widget.recipeDetails.description ?? '');
    ingredients = widget.recipeDetails.ingredients ?? [];
    nutritionFacts = widget.recipeDetails.nutritionInfo ?? NutritionInfo();
    directions = List.from(widget.recipeDetails.directions ?? []);
    recipeMedia = List.from(widget.recipeDetails.recipeMedia ?? []);
    servingIdeas = widget.recipeDetails.servingIdeas ?? '';
    wine = widget.recipeDetails.wine ?? '';
    author = widget.recipeDetails.author ?? '';
    authorMediaUrl = widget.recipeDetails.authorMediaUrl ?? '';
    copyright = widget.recipeDetails.copyright ?? '';
    source = widget.recipeDetails.source ?? '';
    notes = widget.recipeDetails.notes ?? '';
    _category = widget.recipeDetails.category ??
        (widget.categories.isNotEmpty ? widget.categories.first.name : null);
    _cuisine = widget.recipeDetails.cuisine ??
        (widget.cuisines.isNotEmpty ? widget.cuisines.first.name : null);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeProviders();
      setState(() => _isLoading = false);
      final recipeState = ref.watch(recipeNotifierProvider);
      if (recipeState.data != null && recipeState.data!.isNotEmpty) {
        final index = recipeState.data!
            .indexWhere((recipe) => recipe.id == widget.recipeId);
        if (index != -1) {
          setState(() {
            _selectedCookbookIndex = index;
          });
        }
      }
    });
  }

  void _initializeProviders() {
    final categoryId = widget.categories
        .firstWhere(
          (c) => c.name == _category,
          orElse: () => widget.categories.isNotEmpty
              ? widget.categories.first
              : Categories(id: 0, name: 'Unknown'),
        )
        .id;
    final cuisineId = widget.cuisines
        .firstWhere(
          (c) => c.name == _cuisine,
          orElse: () => widget.cuisines.isNotEmpty
              ? widget.cuisines.first
              : Cuisines(id: 0, name: 'Unknown'),
        )
        .id;

    ref.read(directionStepsProvider.notifier).setDirections(directions);
    ref.read(ingredientsProvider.notifier)
      ..clearIngredients()
      ..updateIngredients(ingredients);

    ref.read(recipeMetadataProvider.notifier).updateAll(
          recipeId: widget.recipeId,
          cookbookId: widget.cookbookId,
          recipesList: widget.recipesList,
          recipeDetails: widget.recipeDetails,
          categoryId: categoryId,
          cuisineId: cuisineId,
        );

    ref.read(recipeMetadataProvider.notifier)
      ..updateYield(widget.recipeDetails.yield)
      ..updateServings(widget.recipeDetails.servings)
      ..updatePrepTime(widget.recipeDetails.prepTime)
      ..updateCookTime(widget.recipeDetails.cookTime)
      ..updateTotalTime(widget.recipeDetails.totalTime)
      ..updateAuthor(widget.recipeDetails.author)
      ..updateRecipeThumbnailFileUrl(
          widget.recipeDetails.recipeThumbnailFileUrl)
      ..updateAuthor(widget.recipeDetails.author)
      ..updateAuthorMediaUrl(widget.recipeDetails.authorMediaUrl)
      ..updateCopyright(widget.recipeDetails.copyright)
      ..updateSource(widget.recipeDetails.source)
      ..updateServingIdeas(widget.recipeDetails.servingIdeas)
      ..updateDirections(directions)
      ..updateWine(widget.recipeDetails.wine);

    ref.read(notesProvider.notifier).setNote(widget.recipeDetails.notes ?? '');
    ref.read(authorProvider.notifier)
      ..updateAuthorName(widget.recipeDetails.author ?? '')
      ..updateSource(widget.recipeDetails.source ?? '')
      ..updateCopyright(widget.recipeDetails.copyright ?? '');
  }

  @override
  void dispose() {
    recipeNameController.dispose();
    recipeDescController.dispose();
    searchController.dispose();
    super.dispose();
  }

  void updateData() {
    if (recipeNameController.text.trimRight().isEmpty) {
      Utils().showSnackBar(context, "Please enter a recipe name");
      return;
    }

    final metadata = ref.read(recipeMetadataProvider);
    if (metadata.categoryId == null) {
      Utils().showSnackBar(context, "Please select a category");
      return;
    }
    if (metadata.cuisineId == null) {
      Utils().showSnackBar(context, "Please select a cuisine");
      return;
    }

    final authorData = ref.read(authorProvider);
    final notes = ref.read(notesProvider);
    ingredients = ref.read(ingredientsProvider);

    final updatedMetadata = RecipeMetadata(
      recipeId: widget.recipeId,
      cookbookId: widget.cookbookId,
      recipesList: widget.recipesList,
      name: recipeNameController.text,
      description: recipeDescController.text,
      ingredients: ingredients,
      nutritionFacts: nutritionFacts,
      author: authorData.authorName ?? author,
      authorMediaUrl: authorData.image?.path ?? authorMediaUrl,
      copyright: authorData.copyright ?? copyright,
      source: authorData.source ?? source,
      directions: directions,
      servingIdeas: metadata.servingIdeas ?? servingIdeas,
      wine: metadata.wine ?? wine,
      recipeMedia: recipeMedia,
      categoryId: metadata.categoryId,
      cuisineId: metadata.cuisineId,
      yieldValue: metadata.yieldValue,
      servings: metadata.servings,
      prepTime: metadata.prepTime,
      cookTime: metadata.cookTime,
      totalTime: metadata.totalTime,
      wineDesc: metadata.wineDesc,
    );

    ref.read(recipeMetadataProvider.notifier).updateAll(
          recipeId: updatedMetadata.recipeId!,
          cookbookId: updatedMetadata.cookbookId!,
          recipesList: updatedMetadata.recipesList!,
          recipeDetails: RecipeDetails(
            name: updatedMetadata.name,
            description: updatedMetadata.description,
            ingredients: updatedMetadata.ingredients,
            nutritionInfo: updatedMetadata.nutritionFacts,
            author: updatedMetadata.author,
            authorMediaUrl: updatedMetadata.authorMediaUrl,
            copyright: updatedMetadata.copyright,
            source: updatedMetadata.source,
            directions: updatedMetadata.directions,
            servingIdeas: updatedMetadata.servingIdeas,
            wine: updatedMetadata.wine,
            recipeMedia: updatedMetadata.recipeMedia,
            category: _category,
            cuisine: _cuisine,
            servings: updatedMetadata.servings,
            prepTime: updatedMetadata.prepTime,
            cookTime: updatedMetadata.cookTime,
            totalTime: updatedMetadata.totalTime,
          ),
          categoryId: updatedMetadata.categoryId,
          cuisineId: updatedMetadata.cuisineId,
        );

    ref.read(notesProvider.notifier).setNote(notes ?? '');
    ref.read(authorProvider.notifier)
      ..updateAuthorName(authorData.authorName ?? '')
      ..updateSource(authorData.source ?? '')
      ..updateCopyright(authorData.copyright ?? '');
  }

  Future<void> _updateRecipeAndRefresh(CreateRecipeRequest request) async {
    final result = await ref.read(recipeNotifierProvider.notifier).updateRecipe(
          context: context,
          request: request,
          cookbookId: widget.cookbookId,
          recipeId: widget.recipeId,
        );

    if (result) {
      await ref.read(recipeNotifierProvider.notifier).fetchRecipes(
            cookbookId: widget.cookbookId,
            cookbookName: recipeNameController.text,
            reset: true,
            context: context,
            currentPage: 1,
          );

      final updatedState = ref.read(recipeNotifierProvider);
      setState(() {
        widget.recipesList = updatedState.data ?? widget.recipesList;
        isUpdate = true;
      });

      await ref.read(singleRecipeNotifierProvider.notifier).fetchRecipe(
            context: context,
            cookbookId: widget.cookbookId ?? 0,
            recipeId: widget.recipeId,
          );
    }
  }

  void _saveIngredient(BuildContext context) async {
    updateData();
    final metadata = ref.read(recipeMetadataProvider);
    if (metadata.categoryId == null || metadata.cuisineId == null) {
      Utils().showSnackBar(context, "Please select category and cuisine");
      return;
    }

    final request = CreateRecipeRequest(
      type: RecipeSection.INGREDIENT.name,
      categoryId: metadata.categoryId!,
      cuisineId: metadata.cuisineId!,
      name: recipeNameController.text,
      ingredients: ingredients.isEmpty ? null : ingredients,
    );

    await _updateRecipeAndRefresh(request);
  }

  void _saveAuthor(BuildContext context) async {
    updateData();
    final metadata = ref.read(recipeMetadataProvider);
    if (metadata.categoryId == null) {
      Utils().showSnackBar(context, "Please select a category");
      return;
    }
    if (metadata.cuisineId == null) {
      Utils().showSnackBar(context, "Please select a cuisine");
      return;
    }
    final authorData = ref.read(authorProvider);
    // Create UpdateRecipeRequest (adjust fields based on your API model)

    final request = CreateRecipeRequest(
      type: RecipeSection.AUTHOR.name,
      categoryId: metadata.categoryId!,
      existingAuthorMediaFileId: authorData.image != null
          ? null
          : widget.recipeDetails.authorMediaFileId,
      cuisineId: metadata.cuisineId!,
      name: recipeNameController.text,
      authorName:
          authorData.authorName?.isEmpty ?? true ? null : authorData.authorName,
      authorSource:
          authorData.source?.isEmpty ?? true ? null : authorData.source,
      authorCopyright:
          authorData.copyright?.isEmpty ?? true ? null : authorData.copyright,
      authorProfileFile: authorData.image,
    );

    updateRecipe(request);
  }

  void _saveNotes(BuildContext context) async {
    updateData();
    final metadata = ref.read(recipeMetadataProvider);
    if (metadata.categoryId == null) {
      Utils().showSnackBar(context, "Please select a category");
      return;
    }
    if (metadata.cuisineId == null) {
      Utils().showSnackBar(context, "Please select a cuisine");
      return;
    }
    final notes = ref.read(notesProvider);
    // Create UpdateRecipeRequest (adjust fields based on your API model)
    final request = CreateRecipeRequest(
      type: RecipeSection.NOTES.name,
      categoryId: metadata.categoryId!,
      cuisineId: metadata.cuisineId!,
      name: recipeNameController.text,
      notes: notes ?? '',
    );

    print("Recipe JSON: ${request.toJsonString()}");

    updateRecipe(request);
  }

  void _saveBasicInfo(BuildContext context) async {
    updateData();
    final metadata = ref.read(recipeMetadataProvider);
    if (metadata.categoryId == null) {
      Utils().showSnackBar(context, "Please select a category");
      return;
    }
    if (metadata.cuisineId == null) {
      Utils().showSnackBar(context, "Please select a cuisine");
      return;
    }

    // Create UpdateRecipeRequest (adjust fields based on your API model)
    final request = CreateRecipeRequest(
      type: RecipeSection.BASIC.name,
      name: recipeNameController.text,
      description: recipeDescController.text ?? '',
      categoryId: metadata.categoryId!,
      cuisineId: metadata.cuisineId!,
      yieldValue: metadata.yieldValue ?? '',
      servings: metadata.servings,
      prepTime: metadata.prepTime ?? '',
      cookTime: metadata.cookTime ?? '',
      totalTime: metadata.totalTime ?? '',
    );

    print("Recipe JSON: ${request.toJsonString()}");

    updateRecipe(request);
  }

  void _saveDirections(BuildContext context) async {
    updateData();
    final metadata = ref.read(recipeMetadataProvider);
    if (metadata.categoryId == null) {
      Utils().showSnackBar(context, "Please select a category");
      return;
    }
    if (metadata.cuisineId == null) {
      Utils().showSnackBar(context, "Please select a cuisine");
      return;
    }

    final directionJson =
        ref.read(directionStepsProvider.notifier).directionsJson;
    // Filter media files to include only steps with valid media
    final directionMediaFiles = ref
        .read(directionStepsProvider.notifier)
        .mediaFiles!
        .asMap()
        .entries
        .where(
            (entry) => entry.value != null) // Only include non-null media files
        .map((entry) => entry.value)
        .toList();

    // Create UpdateRecipeRequest
    final request = CreateRecipeRequest(
      type: RecipeSection.DIRECTION.name,
      name: recipeNameController.text,
      categoryId: metadata.categoryId!,
      cuisineId: metadata.cuisineId!,
      directionsJson: directionJson,
      directionMediaFiles:
          directionMediaFiles.isEmpty ? null : directionMediaFiles,
    );

    print("Recipe JSON: ${request.toJsonString()}");

    updateRecipe(request);
  }

  void _saveOthers(BuildContext context) async {
    updateData();
    final metadata = ref.read(recipeMetadataProvider);
    if (metadata.categoryId == null) {
      Utils().showSnackBar(context, "Please select a category");
      return;
    }
    if (metadata.cuisineId == null) {
      Utils().showSnackBar(context, "Please select a cuisine");
      return;
    }

    final recipeMetadata = ref.watch(recipeMetadataProvider);
    final wineDesc = recipeMetadata.wineDesc;

    if ((wineDesc == null || wineDesc.isEmpty ?? false) &&
        (metadata.servingIdeas == null || metadata.servingIdeas!.isEmpty ??
            false)) {
      Utils().showFlushbar(context,
          message: "Please add wine & sevings description", isError: true);
      return;
    }
    // Create UpdateRecipeRequest (adjust fields based on your API model)
    final request = CreateRecipeRequest(
      type: RecipeSection.OTHER.name,
      name: recipeNameController.text,
      categoryId: metadata.categoryId!,
      cuisineId: metadata.cuisineId!,
      servingIdeas: metadata.servingIdeas ?? '',
      wine: wineDesc,
    );

    print("Recipe JSON: ${request.toJsonString()}");

    updateRecipe(request);
  }

  void _saveMediaFiles(BuildContext context) async {
    updateData();
    final metadata = ref.read(recipeMetadataProvider);
    if (metadata.categoryId == null) {
      Utils().showSnackBar(context, "Please select a category");
      return;
    }
    if (metadata.cuisineId == null) {
      Utils().showSnackBar(context, "Please select a cuisine");
      return;
    }
    final mediaFiles = ref.watch(mediaFilesProvider);
    final selectedMediaFiles = mediaFiles.mediaFiles
        .map((media) => media.mediaFile)
        .whereType<File>()
        .toList();
    List<int> existingMedia = mediaFiles.mediaFiles
        .map((media) => media.mediaFileId)
        .whereType<int>()
        .toList();

    // Create UpdateRecipeRequest (adjust fields based on your API model)
    final request = CreateRecipeRequest(
      type: RecipeSection.RECIPE_MEDIA.name,
      name: recipeNameController.text,
      categoryId: metadata.categoryId!,
      cuisineId: metadata.cuisineId!,
      existingRecipeMediaFileIds: existingMedia,
      coverRecipeMediaIndex: mediaFiles.coverIndex,
      recipeMediaFiles: selectedMediaFiles.isEmpty ? null : selectedMediaFiles,
    );

    print("Recipe JSON: ${request.toJsonString()}");

    updateRecipe(request);
  }

  Future<void> updateRecipe(CreateRecipeRequest request) async {
    final recipeState = ref.watch(recipeNotifierProvider);
    // //  Call updateRecipe API
    final result = await ref.read(recipeNotifierProvider.notifier).updateRecipe(
        context: context,
        request: request,
        cookbookId: widget.cookbookId,
        recipeId: widget.recipeId);

    if (result) {
      ref.read(recipeNotifierProvider.notifier).fetchRecipes(
            cookbookId: widget.cookbookId,
            cookbookName: recipeNameController.text,
            reset: true,
            context: context,
            currentPage: 1,
          );

      ref.read(singleRecipeNotifierProvider.notifier).fetchRecipe(
          context: context,
          cookbookId: widget.cookbookId ?? 0,
          recipeId: widget.recipeId);

      setState(() {
        isUpdate = true;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }

    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 1200;
    final recipeState = ref.watch(recipeNotifierProvider);

    final ingredientsCard =
        buildIngredientsSection(context, onSaveIngredient: () {
      _saveIngredient(context);
    });
    final nutritionCard =
        buildNutritionSection(context, nutritionFacts, onSaveNutrition: () {});
    final authorCard = buildEditAuthorSection(
        context, author, authorMediaUrl, copyright, source, onSaveAuthor: () {
      _saveAuthor(context);
    });
    final notesCard = buildNotesSection(notes, onUpdateData: () {
      _saveNotes(context);
    });

    return getDeviceType(context).name == 'mobile'
        ? EditRecipeScreenMobile(
            recipeId: widget.recipeId,
            recipesList: widget.recipesList,
            cookbookId: widget.cookbookId,
            recipeDetails: widget.recipeDetails,
            categories: widget.categories,
            cuisines: widget.cuisines,
            initialStep: widget.initialStep,
          )
        : Scaffold(
            drawer: isSmallScreen
                ? CustomDrawer(
                    title: 'Recipes',
                    state: recipeState,
                    buildContent: (dynamic _) => buildRecipeList(
                        recipes: recipeState.data ?? widget.recipesList,
                        selectedCookbookIndex: _selectedCookbookIndex,
                        searchController: searchController,
                        context: context))
                : null,
            appBar: CustomAppBar(
              title: 'Edit Recipe',
              showDrawerIcon: isSmallScreen,
              onPressed: () async {
                if (isUpdate) {
                  final recipeState = ref.watch(recipeNotifierProvider);
                  widget.recipesList = recipeState.data ?? [];
                  context.go(
                    '/cookbook/cookbookDetail/recipeDetail/${widget.recipeId}',
                    extra: {
                      'id': widget.recipeId,
                      'recipeList': widget.recipesList,
                      'recipeName': recipeNameController.text,
                      'cookbookId': widget.cookbookId,
                    },
                  );
                } else {
                  Navigator.pop(context);
                }
              },
            ),
            body: Stack(
              fit: StackFit.expand,
              children: [
                Image.asset(
                  AssetsManager.background_img,
                  fit: BoxFit.cover,
                ),
                getDeviceType(context) == DeviceType.tablet
                    ? TabEditRecipe(
                        recipeNameController: recipeNameController,
                        recipeDescController: recipeDescController,
                        recipeId: widget.recipeId,
                        cookbookId: widget.cookbookId,
                        recipesList: widget.recipesList,
                        recipeDetails: widget.recipeDetails,
                        recipeMedia: recipeMedia,
                        recipeThumbnailFileUrl:
                            widget.recipeDetails.recipeThumbnailFileUrl ?? '',
                        categoriesList: widget.categories,
                        cuisinesList: widget.cuisines,
                        saveMediaFiles: () {
                          _saveMediaFiles(context);
                        },
                        saveBasicInfo: () {
                          _saveBasicInfo(context);
                        },
                        saveDirections: () {
                          _saveDirections(context);
                        },
                        saveAuthor: () {
                          _saveAuthor(context);
                        },
                        servingIdeas: servingIdeas,
                        wine: wine,
                        author: author,
                        authorMediaUrl: authorMediaUrl,
                        copyright: copyright,
                        source: source,
                        notes: notes,
                        notesCard: notesCard,
                        ingredientsCard: ingredientsCard,
                        saveOthers: () {
                          _saveOthers(context);
                        },
                      )
                    : Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Expanded(
                            flex: 3,
                            child: Container(
                              color: AppColors.secondaryColor,
                              child: buildRecipeList(
                                  recipes:
                                      recipeState.data ?? widget.recipesList,
                                  selectedCookbookIndex: _selectedCookbookIndex,
                                  searchController: searchController,
                                  context: context),
                            ),
                          ),
                          Expanded(
                            flex: 6,
                            child: SingleChildScrollView(
                              child: Padding(
                                padding: EdgeInsets.only(
                                    left: 20.w,
                                    right: 20.w,
                                    top: 30.h,
                                    bottom: 30.h),
                                child: Container(
                                  decoration: BoxDecoration(
                                    color: AppColors.secondaryColor,
                                    borderRadius: BorderRadius.circular(16),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black
                                            .withValues(alpha: 0.05),
                                        blurRadius: 8,
                                        offset: const Offset(0, 4),
                                      )
                                    ],
                                  ),
                                  child: Padding(
                                    padding: EdgeInsets.only(
                                        right: 30.w, left: 30.w, top: 30.h),
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        buildRecipeHeader(
                                          context,
                                          ref,
                                          recipeNameController:
                                              recipeNameController,
                                          recipeDescController:
                                              recipeDescController,
                                        ),
                                        SizedBox(height: 30.h),
                                        buildMainContent(
                                            recipeMedia: recipeMedia,
                                            recipeThumbnailFileUrl: widget
                                                .recipeDetails
                                                .recipeThumbnailFileUrl,
                                            categoriesList: widget.categories,
                                            cuisinesList: widget.cuisines,
                                            category: _category,
                                            cuisine: _cuisine,
                                            saveMediaFiles: () {
                                              _saveMediaFiles(context);
                                            },
                                            saveBasicInfo: () {
                                              _saveBasicInfo(context);
                                            }),
                                        SizedBox(height: 40.h),
                                        CustomDirectionsWidgets(
                                          isCallFromEdit: true,
                                          callFromClipper: false,
                                          onUpdateData: () {
                                            _saveDirections(context);
                                          },
                                        ),
                                        SizedBox(height: 40.h),
                                        CustomServingWidget(
                                          servingIdeas: servingIdeas,
                                        ),
                                        SizedBox(height: 40.h),
                                        CustomWineWidget(
                                          wine: wine,
                                        ),
                                        SizedBox(height: 40.h),
                                        Center(
                                          child: CustomButton(
                                            text: "Save Changes",
                                            fontSize: 14,
                                            width: 240,
                                            onPressed: () {
                                              _saveOthers(context);
                                            },
                                          ),
                                        ),
                                        SizedBox(height: 40.h),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ),
                          Expanded(
                            flex: 2,
                            child: SingleChildScrollView(
                              child: Padding(
                                padding: EdgeInsets.only(
                                    right: 20.w, top: 30.h, bottom: 30.h),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    ingredientsCard,
                                    SizedBox(height: 30.h),
                                    nutritionCard,
                                    SizedBox(height: 30.h),
                                    authorCard,
                                    SizedBox(height: 30.h),
                                    notesCard,
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
              ],
            ),
          );
  }
}
