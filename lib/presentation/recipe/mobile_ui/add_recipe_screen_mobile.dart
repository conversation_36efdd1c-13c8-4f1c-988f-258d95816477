import 'dart:io';
import 'package:flutter/material.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/core/network/app_status.dart';
import 'package:mastercookai/core/utils/Utils.dart';
import 'package:mastercookai/presentation/recipe/mobile_ui/sub_views/add_subview/add_ingredent_setep5.dart';
import 'package:mastercookai/presentation/recipe/mobile_ui/sub_views/add_subview/custom_author_step6.dart';
import 'package:mastercookai/presentation/recipe/mobile_ui/sub_views/add_subview/recipe_detail_notes_step7.dart';
import 'package:mastercookai/presentation/recipe/mobile_ui/sub_views/add_subview/recipe_detail_step1.dart';
import 'package:mastercookai/presentation/recipe/mobile_ui/sub_views/add_subview/recipe_detail_step8.dart';
import 'package:mastercookai/presentation/recipe/mobile_ui/sub_views/add_subview/recipe_directions_step4.dart'
    hide ingredientsProvider;
import 'package:mastercookai/presentation/recipe/mobile_ui/sub_views/add_subview/recipe_media_Step2.dart';
import 'package:mastercookai/presentation/recipe/mobile_ui/sub_views/add_subview/recipe_update_info_step3.dart';
import '../../../app/imports/packages_imports.dart';
import '../../../core/data/models/cookbook.dart';
import '../../../core/data/models/recipe_detail_response.dart';
import '../../../core/data/request_query/create_recipe_request.dart';
import '../../../core/providers/categories_notifier.dart';
import '../../../core/providers/cookbook_notifier.dart';
import '../../../core/providers/cuisines_notifier.dart';
import '../../../core/providers/recipe/author_provider.dart';
import '../../../core/providers/recipe/directions_provider.dart';
import '../../../core/providers/recipe/ingrident_provider.dart';
import '../../../core/providers/recipe/media_provider.dart';
import '../../../core/providers/recipe/metadata_provider.dart';
import '../../../core/providers/recipe/notes_provider.dart';
import '../../../core/providers/recipe/step_Provider.dart';
import '../../../core/providers/recipe_notifier.dart';
import '../../../core/widgets/custom_loading.dart';
import '../../cookbook/widgets/custom_title_text.dart';
import 'sub_views/add_recipe_appbar.dart';

class AddRecipeScreenMobile extends ConsumerStatefulWidget {
  const AddRecipeScreenMobile(
      {super.key,
      this.selectedCookbook,
      this.callFromClipper,
      this.clipperNote,
      this.recipeDetails});

  final Cookbook? selectedCookbook;
  final bool? callFromClipper;
  final String? clipperNote;
  final RecipeDetails? recipeDetails;

  @override
  ConsumerState<AddRecipeScreenMobile> createState() => _AddRecipeScreenState();
}

class _AddRecipeScreenState extends ConsumerState<AddRecipeScreenMobile> {
  late Cookbook? selectedCookbook;
  TextEditingController recipeNameController = TextEditingController();
  TextEditingController recipeDescController = TextEditingController();
  final TextEditingController searchController = TextEditingController();
  bool _isLoading = true;
  RecipeDetails? recipeDetails;
  final formKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    selectedCookbook = widget.selectedCookbook;

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _updateRecipeDetails();
      setState(() => _isLoading = false);
    });
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }

    // Listen to recipeNotifierProvider changes to handle side effects
    ref.listen<AppState>(recipeNotifierProvider, (previous, next) {
      if (next.status == AppStatus.loading) {
        ref.read(loadingProvider.notifier).state = true;
      } else if (next.status == AppStatus.success) {
        ref.read(loadingProvider.notifier).state = false;
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (!mounted) return;
          Utils().showFlushbar(
            context,
            message: 'Recipe saved successfully!',
            isError: false,
            onDismissed: () {
              Future.delayed(const Duration(milliseconds: 100), () {
                if (mounted) {
                  Navigator.of(context).pop();
                }
              });
            },
          );
        });
      } else if (next.status == AppStatus.error) {
        ref.read(loadingProvider.notifier).state = false;
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (!mounted) return;
          Utils().showFlushbar(
            context,
            message: next.errorMessage ?? 'Failed to save recipe',
            isError: true,
          );
        });
      }
    });

    final stepNotifier = ref.read(stepProvider.notifier);

    return Scaffold(
        appBar: AddRecipeAppBar(
          totalStep: 8,
          title: selectedCookbook?.name ?? 'Add Recipe',
          onBack: () {
            if (stepNotifier.state.current == 1) {
              ref.read(directionStepsProvider.notifier).resetWithDefaults( defaultCount: 0);
              Navigator.of(context).pop();
            } else {
              stepNotifier.prevStep();
            }
          },
        ),
        body: _buildTabContent());
  }

  Widget _buildTabContent() {
    final currentStep = ref.watch(stepProvider).current;

    final categoriesState = ref.watch(categoriesNotifierProvider);
    final cuisinesState = ref.watch(cuisinesNotifierProvider);

    switch (currentStep) {
      case 1:
        return recipeDetailStep1(
            recipeNameController: recipeNameController,
            recipeDescController: recipeDescController,
            stepNotifier: ref.read(stepProvider.notifier),
            context: context,
            formKey: formKey,
            callFromClipper: widget.callFromClipper ?? false);
      case 2:
        return recipeMediaStep2(
          context: context,
          recipeMedia: widget.recipeDetails?.recipeMedia ?? [],
          isCallFromEdit: false,
          recipeThumbnailFileUrl:
              widget.recipeDetails?.recipeThumbnailFileUrl ?? "",
          stepNotifier: ref.read(stepProvider.notifier),
        );
      case 3:
        return RecipeUpdateInfoStep3(
          categoriesState.data ?? [],
          cuisinesState.data ?? [],
          callFromUpdate: false,
        );
      case 4:
        return RecipeDirectionsStep4();
      case 5:
        return AddIngredentSetep5(isCallFromEdit: false);
      case 6:
        return CustomAuthorStep6();
      case 7:
        return RecipeDetailNotesStep7();
      case 8:
        return recipeDetailStep8(
            context: context,
            stepNotifier: ref.read(stepProvider.notifier),
            onSaveRecipe: () {
              onSavePressed(context, ref);
            });
      default:
        return const SizedBox.shrink();
    }
  }

  void onSavePressed(BuildContext context, WidgetRef ref) async {
    if (selectedCookbook == null) {
      Utils().showFlushbar(context,
          message: 'No cookbook selected', isError: true);
      return;
    }

    final metadata = ref.read(recipeMetadataProvider);
    final authorData = ref.read(authorProvider);
    final ingredients = ref.read(ingredientsProvider);
    final mediaFiles = ref.read(mediaFilesProvider);
    final directionNotifier = ref.read(directionStepsProvider.notifier);
    final directionJson = directionNotifier.directionsJson;
    final directionMediaFiles = directionNotifier.mediaFiles;
    final notes = ref.read(notesProvider);

    if (recipeNameController.text.trimRight().isEmpty) {
      Utils().showFlushbar(context,
          message: 'Please enter a recipe name', isError: true);
      return;
    }
    if (metadata.categoryId == null || metadata.categoryId == 0) {
      Utils().showFlushbar(context,
          message: 'Please select a category', isError: true);
      return;
    }
    if (metadata.cuisineId == null || metadata.cuisineId == 0) {
      Utils().showFlushbar(context,
          message: 'Please select a cuisine', isError: true);
      return;
    }

    final selectedMediaFiles = mediaFiles.mediaFiles
        .map((media) => media.mediaFile)
        .whereType<File>()
        .toList();
    final mediaUrl = mediaFiles.mediaFiles
        .map((media) => media.mediaUrl)
        .firstWhere((url) => url != null && url.isNotEmpty, orElse: () => null);

    final request = CreateRecipeRequest(
        type: '',
        name: recipeNameController.text,
        coverRecipeMediaIndex: mediaFiles.coverIndex,
        description: recipeDescController.text,
        categoryId: metadata.categoryId!,
        cuisineId: metadata.cuisineId!,
        yieldValue: metadata.yieldValue ?? '',
        servings: metadata.servings,
        prepTime: metadata.prepTime ?? '',
        cookTime: metadata.cookTime ?? '',
        totalTime: metadata.totalTime ?? '',
        recipeMediaFiles:
            selectedMediaFiles.isEmpty ? null : selectedMediaFiles,
        directionsJson: directionJson ?? '',
        directionMediaFiles:
            directionMediaFiles!.isEmpty ? null : directionMediaFiles,
        servingIdeas: metadata.servingIdeas ?? '',
        wine: metadata.wineDesc ?? '',
        ingredients: ingredients.isEmpty ? null : ingredients,
        authorName: authorData.authorName?.isEmpty ?? true
            ? null
            : authorData.authorName,
        authorSource:
            authorData.source?.isEmpty ?? true ? null : authorData.source,
        authorCopyright:
            authorData.copyright?.isEmpty ?? true ? null : authorData.copyright,
        authorProfileFile: authorData.image,
        notes: notes ?? '',
        AiImageUrl: mediaUrl,
        AiMessageId: metadata.AiMessageId);

    final cookbookId = selectedCookbook!.id;

    final result = await ref.read(recipeNotifierProvider.notifier).createRecipe(
          context: context,
          request: request,
          cookbookId: cookbookId,
        );
    print("request to Add Recipe ${request.toJson()} ");

    if (result) {
      ref.read(stepProvider.notifier).setStep(1);
      ref.read(recipeNotifierProvider.notifier).showAddRecipeSuccessBottomSheet(
          context: context, ref: ref, onSuccess: () {});
      WidgetsBinding.instance.addPostFrameCallback((_) async {
        ref.read(loadingProvider.notifier).state = false;
        ref.read(recipeNotifierProvider.notifier).fetchRecipes(
              cookbookId: cookbookId,
              cookbookName: recipeNameController.text,
              cuisineId: 0,
              categoryId: 0,
              reset: true,
              context: context,
              currentPage: 1,
            );
        await ref.read(cookbookNotifierProvider.notifier).fetchCookbooks(
              context: context,
              loadMore: false,
            );
      });
      if (widget.callFromClipper ?? false) {
        context.go(
            '/cookbook/cookbookDetail?id=${widget.selectedCookbook?.id}&name=${Uri.encodeComponent(widget.selectedCookbook?.name ?? '')}');
      }
    }
  }

  @override
  void didUpdateWidget(covariant AddRecipeScreenMobile oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.recipeDetails != oldWidget.recipeDetails) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _updateRecipeDetails();
      });
    }
  }

  void _updateRecipeDetails() {
    // Reset loading state
    ref.read(loadingProvider.notifier).state = false;

    recipeDetails = widget.recipeDetails;
    if ((widget.callFromClipper ?? false) && recipeDetails != null) {
      // Log all fields for debugging
      print("RecipeDetails: ${recipeDetails?.toJson()}");

      // Initialize controllers
      recipeNameController.text = recipeDetails?.name ?? '';
      recipeDescController.text = (recipeDetails?.description?.isEmpty ?? false)
          ? widget.clipperNote ?? ''
          : recipeDetails?.description ?? '';

      // Initialize providers
      // Ingredients
      ref.read(ingredientsProvider.notifier)
        ..clearIngredients()
        ..updateIngredients(recipeDetails?.ingredients ?? []);

      // Directions
      ref.read(directionStepsProvider.notifier).setDirections(
            recipeDetails?.directions ?? [],
          );
      final mediaList =
          List<RecipeMedia>.from(recipeDetails?.recipeMedia ?? []);

      // Metadata
      ref.read(recipeMetadataProvider.notifier).set(
          name: recipeDetails?.name,
          description: recipeDetails?.description,
          ingredients: recipeDetails?.ingredients,
          //  nutritionFacts: recipeDetails?.nutritionInfo,
          author: recipeDetails?.author,
          authorMediaUrl: recipeDetails?.authorMediaUrl,
          copyright: recipeDetails?.copyright,
          source: recipeDetails?.source,
          directions: recipeDetails?.directions,
          servingIdeas: recipeDetails?.servingIdeas,
          wine: recipeDetails?.wine,
          recipeMedia: recipeDetails?.recipeMedia,
          categoryId: recipeDetails?.categoryId ?? null,
          cuisineId: recipeDetails?.cuisineId ?? null,
          yieldValue: recipeDetails?.yield,
          // Changed from yieldUnit to yield
          servings: recipeDetails?.servings,
          prepTime: recipeDetails?.prepTime,
          cookTime: recipeDetails?.cookTime,
          totalTime: recipeDetails?.totalTime,
          wineDesc: recipeDetails?.wine,
          AiImageUrl: recipeDetails?.aiImageUrl,
          AiMessageId: recipeDetails?.aiMessageId?.toString());

      // Author
      ref.read(authorProvider.notifier)
        ..updateAuthorName(recipeDetails?.author ?? '')
        ..updateSource(recipeDetails?.source ?? '')
        ..updateCopyright(recipeDetails?.copyright ?? '');

      // Notes
      ref
          .read(notesProvider.notifier)
          .setNote(recipeDetails?.notes ?? widget.clipperNote);

      if (recipeDetails?.aiImageUrl?.isNotEmpty == true) {
        mediaList.insert(
          0,
          RecipeMedia(
            mediaUrl: '',
          ),
        );
        mediaList.insert(
          1,
          RecipeMedia(
            mediaUrl: recipeDetails?.aiImageUrl!,
          ),
        );
      }

      ref.read(mediaFilesProvider.notifier).set(mediaList);

      // Media Files
      ref
          .read(mediaFilesProvider.notifier)
          .updateMedia(mediaList, recipeDetails?.coverMediaIndex ?? 0);
    } else {
      // Reset providers to default state if recipeDetails is null
      ref.read(ingredientsProvider.notifier).resetWithDefaults();
       ref.read(recipeMetadataProvider.notifier).reset();
      ref.read(authorProvider.notifier).reset();
      ref.read(notesProvider.notifier).reset();
      ref.read(mediaFilesProvider.notifier).clear();
      recipeNameController.text = '';
      recipeDescController.text = '';
    }
  }

  @override
  void dispose() {
    recipeNameController.dispose();
    recipeDescController.dispose();
    searchController.dispose();
    super.dispose();
  }
}
