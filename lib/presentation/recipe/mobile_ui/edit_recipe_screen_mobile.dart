import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/presentation/recipe/mobile_ui/sub_views/add_recipe_appbar.dart';
import 'package:mastercookai/presentation/recipe/mobile_ui/sub_views/edit_subview/edit_recipe_author_step6.dart';
import 'package:mastercookai/presentation/recipe/mobile_ui/sub_views/edit_subview/edit_recipe_directions_step3.dart';
import 'package:mastercookai/presentation/recipe/mobile_ui/sub_views/edit_subview/edit_recipe_info_step1.dart';
import 'package:mastercookai/presentation/recipe/mobile_ui/sub_views/edit_subview/edit_recipe_ingridient_Step5.dart';
import 'package:mastercookai/presentation/recipe/mobile_ui/sub_views/edit_subview/edit_recipe_media_Step2.dart';
import 'package:mastercookai/presentation/recipe/mobile_ui/sub_views/edit_subview/edit_recipe_notes_Step4.dart';
import 'package:mastercookai/presentation/recipe/mobile_ui/sub_views/edit_subview/edit_recipe_other_step7.dart';
import '../../../app/imports/packages_imports.dart';
import '../../../core/data/models/category_response.dart';
import '../../../core/data/models/cuisines_response.dart';
import '../../../core/data/models/recipe_detail_response.dart';
import '../../../core/data/models/recipe_response.dart';
import '../../../core/data/request_query/create_recipe_request.dart';
import '../../../core/data/request_query/recipe_meta_data.dart';
import '../../../core/providers/recipe/author_provider.dart';
import '../../../core/providers/recipe/directions_provider.dart';
import '../../../core/providers/recipe/ingrident_provider.dart';
import '../../../core/providers/recipe/media_provider.dart';
import '../../../core/providers/recipe/metadata_provider.dart';
import '../../../core/providers/recipe/notes_provider.dart';
import '../../../core/providers/recipe/step_Provider.dart';
import '../../../core/providers/recipe_notifier.dart';
import '../../../core/providers/single_recipe_notifier.dart';
import '../../../core/utils/Utils.dart';

class EditRecipeScreenMobile extends ConsumerStatefulWidget {
  final int recipeId;
  final int cookbookId;
  List<Recipe> recipesList;
  final RecipeDetails recipeDetails;
  final List<Categories> categories;
  final List<Cuisines> cuisines;
  final int? initialStep;

  EditRecipeScreenMobile({
    super.key,
    required this.recipeId,
    required this.cookbookId,
    required this.recipesList,
    required this.recipeDetails,
    required this.categories,
    required this.cuisines,
    this.initialStep,
  });

  @override
  ConsumerState<EditRecipeScreenMobile> createState() =>
      _EditRecipeScreenState();
}

class _EditRecipeScreenState extends ConsumerState<EditRecipeScreenMobile> {
  final TextEditingController searchController = TextEditingController();
  int _selectedCookbookIndex = 0;
  late TextEditingController recipeNameController;
  late TextEditingController recipeDescController;
  late List<String> ingredients;
  late NutritionInfo nutritionFacts;
  late List<Directions> directions;
  late List<RecipeMedia> recipeMedia;
  late String servingIdeas;
  late String wine;
  late String author;
  late String authorMediaUrl;
  late String copyright;
  late String source;
  late String notes;
  String? _category;
  String? _cuisine;
  bool _isLoading = true;
  bool isUpdate = false;

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  void _initializeData() {
    recipeNameController =
        TextEditingController(text: widget.recipeDetails.name ?? '');
    recipeDescController =
        TextEditingController(text: widget.recipeDetails.description ?? '');
    ingredients = widget.recipeDetails.ingredients ?? [];
    nutritionFacts = widget.recipeDetails.nutritionInfo ?? NutritionInfo();
    directions = List.from(widget.recipeDetails.directions ?? []);
    recipeMedia = List.from(widget.recipeDetails.recipeMedia ?? []);
    servingIdeas = widget.recipeDetails.servingIdeas ?? '';
    wine = widget.recipeDetails.wine ?? '';
    author = widget.recipeDetails.author ?? '';
    authorMediaUrl = widget.recipeDetails.authorMediaUrl ?? '';
    copyright = widget.recipeDetails.copyright ?? '';
    source = widget.recipeDetails.source ?? '';
    notes = widget.recipeDetails.notes ?? '';
    _category = widget.recipeDetails.category ??
        (widget.categories.isNotEmpty ? widget.categories.first.name : null);
    _cuisine = widget.recipeDetails.cuisine ??
        (widget.cuisines.isNotEmpty ? widget.cuisines.first.name : null);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeProviders();

      // Set initial step if provided
      if (widget.initialStep != null) {
        ref.read(stepProvider.notifier).setStep(widget.initialStep!);
      }

      setState(() => _isLoading = false);
      final recipeState = ref.watch(recipeNotifierProvider);
      if (recipeState.data != null && recipeState.data!.isNotEmpty) {
        final index = recipeState.data!
            .indexWhere((recipe) => recipe.id == widget.recipeId);
        if (index != -1) {
          setState(() {
            _selectedCookbookIndex = index;
          });
        }
      }
    });
  }

  void _initializeProviders() {
    final categoryId = widget.categories
        .firstWhere(
          (c) => c.name == _category,
          orElse: () => widget.categories.isNotEmpty
              ? widget.categories.first
              : Categories(id: 0, name: 'Unknown'),
        )
        .id;
    final cuisineId = widget.cuisines
        .firstWhere(
          (c) => c.name == _cuisine,
          orElse: () => widget.cuisines.isNotEmpty
              ? widget.cuisines.first
              : Cuisines(id: 0, name: 'Unknown'),
        )
        .id;

    ref.read(directionStepsProvider.notifier).setDirections(directions);
    ref.read(ingredientsProvider.notifier)
      ..clearIngredients()
      ..updateIngredients(ingredients);

    ref.read(recipeMetadataProvider.notifier).updateAll(
          recipeId: widget.recipeId,
          cookbookId: widget.cookbookId,
          recipesList: widget.recipesList,
          recipeDetails: widget.recipeDetails,
          categoryId: categoryId,
          cuisineId: cuisineId,
        );

    ref.read(recipeMetadataProvider.notifier)
      ..updateYield(widget.recipeDetails.yield)
      ..updateServings(widget.recipeDetails.servings)
      ..updatePrepTime(widget.recipeDetails.prepTime)
      ..updateCookTime(widget.recipeDetails.cookTime)
      ..updateTotalTime(widget.recipeDetails.totalTime)
      ..updateAuthor(widget.recipeDetails.author)
      ..updateRecipeThumbnailFileUrl(
          widget.recipeDetails.recipeThumbnailFileUrl)
      ..updateAuthor(widget.recipeDetails.author)
      ..updateAuthorMediaUrl(widget.recipeDetails.authorMediaUrl)
      ..updateCopyright(widget.recipeDetails.copyright)
      ..updateSource(widget.recipeDetails.source)
      ..updateServingIdeas(widget.recipeDetails.servingIdeas)
      ..updateDirections(directions)
      ..updateWine(widget.recipeDetails.wine);

    ref.read(notesProvider.notifier).setNote(widget.recipeDetails.notes ?? '');
    ref.read(authorProvider.notifier)
      ..updateAuthorName(widget.recipeDetails.author ?? '')
      ..updateSource(widget.recipeDetails.source ?? '')
      ..updateCopyright(widget.recipeDetails.copyright ?? '');
  }

  @override
  void dispose() {
    recipeNameController.dispose();
    recipeDescController.dispose();
    searchController.dispose();
    super.dispose();
  }

  void updateData() {
    if (recipeNameController.text.trimRight().isEmpty) {
      Utils().showSnackBar(context, "Please enter a recipe name");
      return;
    }

    final metadata = ref.read(recipeMetadataProvider);
    if (metadata.categoryId == null) {
      Utils().showSnackBar(context, "Please select a category");
      return;
    }
    if (metadata.cuisineId == null) {
      Utils().showSnackBar(context, "Please select a cuisine");
      return;
    }

    final authorData = ref.read(authorProvider);
    final notes = ref.read(notesProvider);
    ingredients = ref.read(ingredientsProvider);

    final updatedMetadata = RecipeMetadata(
      recipeId: widget.recipeId,
      cookbookId: widget.cookbookId,
      recipesList: widget.recipesList,
      name: recipeNameController.text,
      description: recipeDescController.text,
      ingredients: ingredients,
      nutritionFacts: nutritionFacts,
      author: authorData.authorName ?? author,
      authorMediaUrl: authorData.image?.path ?? authorMediaUrl,
      copyright: authorData.copyright ?? copyright,
      source: authorData.source ?? source,
      directions: directions,
      servingIdeas: metadata.servingIdeas ?? servingIdeas,
      wine: metadata.wine ?? wine,
      recipeMedia: recipeMedia,
      categoryId: metadata.categoryId,
      cuisineId: metadata.cuisineId,
      yieldValue: metadata.yieldValue,
      servings: metadata.servings,
      prepTime: metadata.prepTime,
      cookTime: metadata.cookTime,
      totalTime: metadata.totalTime,
      wineDesc: metadata.wineDesc,
    );

    ref.read(recipeMetadataProvider.notifier).updateAll(
          recipeId: updatedMetadata.recipeId!,
          cookbookId: updatedMetadata.cookbookId!,
          recipesList: updatedMetadata.recipesList!,
          recipeDetails: RecipeDetails(
            name: updatedMetadata.name,
            description: updatedMetadata.description,
            ingredients: updatedMetadata.ingredients,
            nutritionInfo: updatedMetadata.nutritionFacts,
            author: updatedMetadata.author,
            authorMediaUrl: updatedMetadata.authorMediaUrl,
            copyright: updatedMetadata.copyright,
            source: updatedMetadata.source,
            directions: updatedMetadata.directions,
            servingIdeas: updatedMetadata.servingIdeas,
            wine: updatedMetadata.wine,
            recipeMedia: updatedMetadata.recipeMedia,
            category: _category,
            cuisine: _cuisine,
            servings: updatedMetadata.servings,
            prepTime: updatedMetadata.prepTime,
            cookTime: updatedMetadata.cookTime,
            totalTime: updatedMetadata.totalTime,
          ),
          categoryId: updatedMetadata.categoryId,
          cuisineId: updatedMetadata.cuisineId,
        );

    ref.read(notesProvider.notifier).setNote(notes ?? '');
    ref.read(authorProvider.notifier)
      ..updateAuthorName(authorData.authorName ?? '')
      ..updateSource(authorData.source ?? '')
      ..updateCopyright(authorData.copyright ?? '');
  }

  Future<void> _updateRecipeAndRefresh(CreateRecipeRequest request) async {
    final result = await ref.read(recipeNotifierProvider.notifier).updateRecipe(
          context: context,
          request: request,
          cookbookId: widget.cookbookId,
          recipeId: widget.recipeId,
        );

    if (result) {
      await ref.read(recipeNotifierProvider.notifier).fetchRecipes(
            cookbookId: widget.cookbookId,
            cookbookName: recipeNameController.text,
            reset: true,
            context: context,
            currentPage: 1,
          );

      final updatedState = ref.read(recipeNotifierProvider);
      setState(() {
        widget.recipesList = updatedState.data ?? widget.recipesList;
        isUpdate = true;
      });

      await ref.read(singleRecipeNotifierProvider.notifier).fetchRecipe(
            context: context,
            cookbookId: widget.cookbookId ?? 0,
            recipeId: widget.recipeId,
          );
    }
  }

  void _saveIngredient(BuildContext context) async {
    updateData();
    final metadata = ref.read(recipeMetadataProvider);
    if (metadata.categoryId == null || metadata.cuisineId == null) {
      Utils().showSnackBar(context, "Please select category and cuisine");
      return;
    }

    final request = CreateRecipeRequest(
      type: RecipeSection.INGREDIENT.name,
      categoryId: metadata.categoryId!,
      cuisineId: metadata.cuisineId!,
      name: recipeNameController.text,
      ingredients: ingredients.isEmpty ? null : ingredients,
    );

    await _updateRecipeAndRefresh(request);
  }

  void _saveAuthor(BuildContext context) async {
    updateData();
    final metadata = ref.read(recipeMetadataProvider);
    if (metadata.categoryId == null) {
      Utils().showSnackBar(context, "Please select a category");
      return;
    }
    if (metadata.cuisineId == null) {
      Utils().showSnackBar(context, "Please select a cuisine");
      return;
    }
    final authorData = ref.read(authorProvider);
    // Create UpdateRecipeRequest (adjust fields based on your API model)

    final request = CreateRecipeRequest(
      type: RecipeSection.AUTHOR.name,
      categoryId: metadata.categoryId!,
      existingAuthorMediaFileId: authorData.image != null
          ? null
          : widget.recipeDetails.authorMediaFileId,
      cuisineId: metadata.cuisineId!,
      name: recipeNameController.text,
      authorName:
          authorData.authorName?.isEmpty ?? true ? null : authorData.authorName,
      authorSource:
          authorData.source?.isEmpty ?? true ? null : authorData.source,
      authorCopyright:
          authorData.copyright?.isEmpty ?? true ? null : authorData.copyright,
      authorProfileFile: authorData.image,
    );

    updateRecipe(request);
  }

  void _saveNotes(BuildContext context) async {
    updateData();
    final metadata = ref.read(recipeMetadataProvider);
    if (metadata.categoryId == null) {
      Utils().showSnackBar(context, "Please select a category");
      return;
    }
    if (metadata.cuisineId == null) {
      Utils().showSnackBar(context, "Please select a cuisine");
      return;
    }
    final notes = ref.read(notesProvider);
    // Create UpdateRecipeRequest (adjust fields based on your API model)
    final request = CreateRecipeRequest(
      type: RecipeSection.NOTES.name,
      categoryId: metadata.categoryId!,
      cuisineId: metadata.cuisineId!,
      name: recipeNameController.text,
      notes: notes ?? '',
    );

    print("Recipe JSON: ${request.toJsonString()}");

    updateRecipe(request);
  }

  void _saveBasicInfo(BuildContext context) async {
    updateData();
    final metadata = ref.read(recipeMetadataProvider);
    if (metadata.categoryId == null) {
      Utils().showSnackBar(context, "Please select a category");
      return;
    }
    if (metadata.cuisineId == null) {
      Utils().showSnackBar(context, "Please select a cuisine");
      return;
    }

    // Create UpdateRecipeRequest (adjust fields based on your API model)
    final request = CreateRecipeRequest(
      type: RecipeSection.BASIC.name,
      name: recipeNameController.text,
      description: recipeDescController.text ?? '',
      categoryId: metadata.categoryId!,
      cuisineId: metadata.cuisineId!,
      yieldValue: metadata.yieldValue ?? '',
      servings: metadata.servings,
      prepTime: metadata.prepTime ?? '',
      cookTime: metadata.cookTime ?? '',
      totalTime: metadata.totalTime ?? '',
    );

    print("Recipe JSON: ${request.toJsonString()}");

    updateRecipe(request);
  }

  void _saveDirections(BuildContext context) async {
    updateData();
    final metadata = ref.read(recipeMetadataProvider);
    if (metadata.categoryId == null) {
      Utils().showSnackBar(context, "Please select a category");
      return;
    }
    if (metadata.cuisineId == null) {
      Utils().showSnackBar(context, "Please select a cuisine");
      return;
    }

    final directionJson =
        ref.read(directionStepsProvider.notifier).directionsJson;
    // Filter media files to include only steps with valid media
    final directionMediaFiles = ref
        .read(directionStepsProvider.notifier)
        .mediaFiles!
        .asMap()
        .entries
        .where(
            (entry) => entry.value != null) // Only include non-null media files
        .map((entry) => entry.value)
        .toList();

    // Create UpdateRecipeRequest
    final request = CreateRecipeRequest(
      type: RecipeSection.DIRECTION.name,
      name: recipeNameController.text,
      categoryId: metadata.categoryId!,
      cuisineId: metadata.cuisineId!,
      directionsJson: directionJson,
      directionMediaFiles:
          directionMediaFiles.isEmpty ? null : directionMediaFiles,
    );

    print("Recipe JSON: ${request.toJsonString()}");

    updateRecipe(request);
  }

  void _saveOthers(BuildContext context) async {
    updateData();
    final metadata = ref.read(recipeMetadataProvider);
    if (metadata.categoryId == null) {
      Utils().showSnackBar(context, "Please select a category");
      return;
    }
    if (metadata.cuisineId == null) {
      Utils().showSnackBar(context, "Please select a cuisine");
      return;
    }

    final recipeMetadata = ref.watch(recipeMetadataProvider);
    final wineDesc = recipeMetadata.wineDesc;

    if ((wineDesc == null || wineDesc.isEmpty ?? false) &&
        (metadata.servingIdeas == null || metadata.servingIdeas!.isEmpty ??
            false)) {
      Utils().showFlushbar(context,
          message: "Please add wine & sevings description", isError: true);
      return;
    }
    // Create UpdateRecipeRequest (adjust fields based on your API model)
    final request = CreateRecipeRequest(
      type: RecipeSection.OTHER.name,
      name: recipeNameController.text,
      categoryId: metadata.categoryId!,
      cuisineId: metadata.cuisineId!,
      servingIdeas: metadata.servingIdeas ?? '',
      wine: wineDesc,
    );

    print("Recipe JSON: ${request.toJsonString()}");

    updateRecipe(request);
  }

  void _saveMediaFiles(BuildContext context) async {
    updateData();
    final metadata = ref.read(recipeMetadataProvider);
    if (metadata.categoryId == null) {
      Utils().showSnackBar(context, "Please select a category");
      return;
    }
    if (metadata.cuisineId == null) {
      Utils().showSnackBar(context, "Please select a cuisine");
      return;
    }
    final mediaFiles = ref.watch(mediaFilesProvider);
    final selectedMediaFiles = mediaFiles.mediaFiles
        .map((media) => media.mediaFile)
        .whereType<File>()
        .toList();
    List<int> existingMedia = mediaFiles.mediaFiles
        .map((media) => media.mediaFileId)
        .whereType<int>()
        .toList();

    // Create UpdateRecipeRequest (adjust fields based on your API model)
    final request = CreateRecipeRequest(
      type: RecipeSection.RECIPE_MEDIA.name,
      name: recipeNameController.text,
      categoryId: metadata.categoryId!,
      cuisineId: metadata.cuisineId!,
      existingRecipeMediaFileIds: existingMedia,
      coverRecipeMediaIndex: mediaFiles.coverIndex,
      recipeMediaFiles: selectedMediaFiles.isEmpty ? null : selectedMediaFiles,
    );

    print("Recipe JSON: ${request.toJsonString()}");

    updateRecipe(request);
  }

  Future<void> updateRecipe(CreateRecipeRequest request) async {
    final recipeState = ref.watch(recipeNotifierProvider);
    // //  Call updateRecipe API
    final result = await ref.read(recipeNotifierProvider.notifier).updateRecipe(
        context: context,
        request: request,
        cookbookId: widget.cookbookId,
        recipeId: widget.recipeId);

    if (result) {
      ref.read(recipeNotifierProvider.notifier).fetchRecipes(
            cookbookId: widget.cookbookId,
            cookbookName: recipeNameController.text,
            reset: true,
            context: context,
            currentPage: 1,
          );

      ref.read(singleRecipeNotifierProvider.notifier).fetchRecipe(
          context: context,
          cookbookId: widget.cookbookId ?? 0,
          recipeId: widget.recipeId);

      setState(() {
        isUpdate = true;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final stepNotifier = ref.read(stepProvider.notifier);

    return Scaffold(
        appBar: AddRecipeAppBar(
          title: 'Edit Recipe',
          totalStep: 7,
          onBack: () {
            if (stepNotifier.state.current == 1) {
              Navigator.of(context).pop();
            } else {
              stepNotifier.prevStep();
            }
          },
        ),
        body: _buildTabContent());
  }

  Widget _buildTabContent() {
    final currentStep = ref.watch(stepProvider).current;

    switch (currentStep) {
      case 1:
        return EditRecipeInfoStep1(
          recipeNameController: recipeNameController,
          recipeDescController: recipeDescController,
          categories: widget.categories,
          cuisines: widget.cuisines,
          recipeDetails: widget.recipeDetails,
          onUpdateData: () {
            _saveBasicInfo(context);
          },
        );
      case 2:
        return editRecipeMediaStep2(
            ref: ref,
            recipeMedia: recipeMedia,
            recipeThumbnailFileUrl: widget.recipeDetails.recipeThumbnailFileUrl,
            onUpdateData: () {
              _saveMediaFiles(context);
            });
      case 3:
        return EditRecipeDirectionsStep3(onUpdateData: () {
          _saveDirections(context);
        });
      case 4:
        return editRecipeNotesStep4(
          notes: widget.recipeDetails.notes ?? '',
          ref: ref,
          onUpdateData: () {
            _saveNotes(context);
          },
        );

      case 5:
        return editRecipeIngridientStep5(
            context: context,
            ref: ref,
            onUpdateData: () {
              _saveIngredient(context);
            });
      case 6:
        return EditRecipeAuthorStep6(
            author: author,
            authorMediaUrl: authorMediaUrl,
            copyright: copyright,
            source: source,
            isCallFromEdit: true,
            onUpdateData: () {

              _saveAuthor(context);
            });
      case 7:
        return editRecipeOtherStep7(
            context: context,
            wine: wine,
            servingIdeas: servingIdeas,
            ref: ref,
            onUpdateData: () {
              _saveOthers(context);
            });

      default:
        return const SizedBox.shrink();
    }
  }
}
