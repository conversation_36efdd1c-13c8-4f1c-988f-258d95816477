import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mastercookai/core/widgets/custom_button.dart';
import 'package:mastercookai/core/widgets/custom_input_field.dart';
import '../../../../../app/imports/packages_imports.dart';
import '../../../../../app/theme/colors.dart';
import '../../../../../core/providers/recipe/notes_provider.dart';
import '../../../../../core/providers/recipe/step_Provider.dart';
import '../../../../../core/widgets/custom_text.dart';
import '../../../subview/custom_ingredent_textfield.dart';

class RecipeDetailNotesStep7 extends ConsumerStatefulWidget {
  bool? isCallFromEdit;
  VoidCallback? onUpdateData;

  RecipeDetailNotesStep7({
    super.key,
    this.isCallFromEdit = false,
    this.onUpdateData,
  });

  @override
  ConsumerState<RecipeDetailNotesStep7> createState() =>
      _CustomNotesWidgetState();
}

class _CustomNotesWidgetState extends ConsumerState<RecipeDetailNotesStep7> {
  late TextEditingController noteController;

  @override
  void initState() {
    super.initState();
    final metadata = ref.read(notesProvider);
    // Initialize noteController with value from notesProvider or widget.notes
    noteController = TextEditingController(
      text: metadata,
    );
  }

  @override
  void dispose() {
    noteController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CustomText(
              text: 'Notes',
              size: 14,
              weight: FontWeight.w500,
              color: AppColors.primaryGreyColor,
            ),
            const SizedBox(height: 16),
            CustomInputField(
              hintText: "Description",
              controller: noteController,
              maxLines: 5,
              onChanged: (val) {
                ref.read(notesProvider.notifier).setNote(val);
              },
            ),
            SizedBox(height: 12.h),
            Visibility(
              visible: widget.isCallFromEdit ?? false,
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 50.0),
                child: CustomButton(
                  text: "Save Changes",
                  fontSize: 14,
                  onPressed: () {
                    // Ensure ingredients are updated before notifying parent
                    widget.onUpdateData?.call();
                  },
                ),
              ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 35),
        child: CustomButton(
          borderRadius: 10,
          text: 'Next',
          onPressed: () {
            ref.read(stepProvider.notifier).nextStep();
          },
        ),
      ),
    );
  }
}
