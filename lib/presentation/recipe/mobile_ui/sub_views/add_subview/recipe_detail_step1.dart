import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mastercookai/core/providers/recipe/step_Provider.dart';
import 'package:mastercookai/core/widgets/custom_input_field.dart';
import 'package:mastercookai/core/widgets/custom_text.dart';

import '../../../../../app/imports/core_imports.dart';
import '../../../../../app/imports/packages_imports.dart';
import '../../../../../core/utils/validator.dart';
import '../../../../../core/widgets/custom_button.dart';
import '../../../subview/custom_ingredent_textfield.dart';

Widget recipeDetailStep1({
  required BuildContext context,
  required TextEditingController recipeNameController,
  required TextEditingController recipeDescController,
  required bool callFromClipper,
  required StepNotifier stepNotifier, required GlobalKey<FormState> formKey,
}) {

  return Scaffold(
    // Optional if you want safe area and scaffold benefits
    body: Padding(
      padding: const EdgeInsets.only(bottom: 70), // Leave space for button
      child: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CustomText(text: 'Add Name & Description ',
              size: 14, weight: FontWeight.w500, color: AppColors.primaryGreyColor),
              SizedBox(height: 16),
              Form(
                key: formKey,
                child: CustomInputField(
                  hintText: 'Recipe Name',
                  controller: recipeNameController,
                  maxLines: 1,
                 verticalPadding: 10,
                  maxLength: 100,
                  validator: Validator.validateRecipeName,
                ),
              ),
              SizedBox(height: 16.h),
              CustomInputField(
                hintText: 'Recipe Description',
                controller: recipeDescController,
                verticalPadding: 10,
                maxLines: 3,
               // height: 200.h,
              ),
            ],
          ),
        ),
      ),
    ),
    bottomNavigationBar: Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 35),
      child: CustomButton(
        borderRadius: 10,
        text: 'Next',
        onPressed: () {
          if (formKey.currentState!
              .validate()) {
            stepNotifier.nextStep();
          }

        },
      ),
    ),
  );
}
