import 'dart:io';
import 'dart:ui';

import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:image_picker/image_picker.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import '../../../../../app/assets_manager.dart';
import '../../../../../app/theme/colors.dart';
import '../../../../../core/providers/recipe/directions_provider.dart';
import '../../../../../core/providers/recipe/step_Provider.dart';
import '../../../../../core/widgets/common_image.dart';
import '../../../../../core/widgets/custom_button.dart';
import '../../../../../core/widgets/custom_ccon_text_button.dart';
import '../../../../../core/widgets/custom_info_cards.dart';
import '../../../../../core/widgets/custom_text.dart';
import '../../../subview/custom_ingredent_textfield.dart';

class RecipeDirectionsStep4 extends ConsumerStatefulWidget {
  const RecipeDirectionsStep4({super.key});

  @override
  RecipeInputScreenState createState() => RecipeInputScreenState();
}

class RecipeInputScreenState extends ConsumerState<RecipeDirectionsStep4> {
  final TextEditingController _descriptionController = TextEditingController();
  final ImagePicker _picker = ImagePicker();
  File? _selectedImage; // Track the selected image for the top input section
  int? _editingIndex; // Track the index of the step being edited

  @override
  void initState() {
    super.initState();
    // Initialize with one empty step
    final directionState = ref.read(directionStepsProvider);

    print("dsfjashfjghsd ${directionState.length}");

    WidgetsBinding.instance.addPostFrameCallback((_) {
      //  ref.read(directionStepsProvider.notifier).resetWithDefaults(defaultCount: 0);
    });
    print("dsfjashfjghsd 11 ${directionState.length}");
  }

  @override
  void dispose() {
    _descriptionController.dispose();
    super.dispose();
  }

  // Function to pick an image for a step or the top input section
  Future<void> _pickImage({int? index}) async {
    final pickedFile = await _picker.pickImage(source: ImageSource.gallery);
    if (pickedFile != null) {
      final file = File(pickedFile.path);
      if (index != null) {
        // Update existing step
        ref.read(directionStepsProvider.notifier).updateStep(
              index,
              mediaFile: file,
              mediaFileName: pickedFile.path.split('/').last,
            );
      } else {
        // Set image for new step (top input section)
        setState(() {
          _selectedImage = file;
        });
      }
    }
  }

  // Function to populate fields for editing a step
  void _editStep(int index) {
    final steps = ref.read(directionStepsProvider);
    final step = steps[index];
    setState(() {
      _editingIndex = index;
      _descriptionController.text = step.description ?? '';
      _selectedImage = step.mediaFile;
    });
  }

  // Function to add a new step or update an existing step from the top input section
  void _addStep() {
    final description = _descriptionController.text.trim();
    if (description.isNotEmpty || _selectedImage != null) {
      if (_editingIndex != null) {
        // Update existing step
        ref.read(directionStepsProvider.notifier).updateStep(
              _editingIndex!,
              description: description,
              mediaFile: _selectedImage,
              mediaFileName: _selectedImage?.path.split('/').last,
            );
      } else {
        // Add new step
        ref.read(directionStepsProvider.notifier).addStep();
        final newIndex = ref.read(directionStepsProvider).length - 1;
        ref.read(directionStepsProvider.notifier).updateStep(
              newIndex,
              description: description,
              mediaFile: _selectedImage,
              mediaFileName: _selectedImage?.path.split('/').last,
            );
      }
      // Clear inputs after adding/updating
      _descriptionController.clear();
      setState(() {
        _selectedImage = null;
        _editingIndex = null; // Reset editing mode
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final steps = ref.watch(directionStepsProvider);
    final stepsNotifier = ref.read(directionStepsProvider.notifier);

    return Scaffold(
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with Directions and Add button
            Row(
              children: [
                CustomText(
                  text: 'Directions',
                  size: 14,
                  weight: FontWeight.w500,
                  color: AppColors.primaryGreyColor,
                ),
                CustomText(
                  text: '(${steps.length} steps)',
                  size: 12,
                  weight: FontWeight.w500,
                  color: AppColors.primaryGreyColor,
                ),
                const Spacer(),
                CustomIconTextButton(
                  iconPath: AssetsManager.ic_plus,
                  text: _editingIndex != null ? 'Update' : 'Add',
                  iconColor: Colors.white,
                  backgroundColor: Colors.blue,
                  textColor: Colors.white,
                  height: 40,
                  onTap: _addStep,
                ),
              ],
            ),
            const SizedBox(height: 16),
            // Input section for new step
            Container(
              padding: const EdgeInsets.all(15),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: AppColors.greyBorderColor, width: 1),
                color: Colors.white,
                boxShadow: const [
                  BoxShadow(
                    color: Colors.black12,
                    blurRadius: 4,
                    offset: Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Image or Placeholder for new step
                  GestureDetector(
                    onTap: () => _pickImage(),
                    child: _selectedImage != null
                        ? ClipRRect(
                            borderRadius: BorderRadius.circular(8),
                            child: CommonImage(
                              imageSource: _selectedImage!,
                              fit: BoxFit.cover,
                              width: double.infinity,
                              height: 100,
                            ),
                          )
                        : DottedBorder(
                            borderType: BorderType.RRect,
                            radius: const Radius.circular(10),
                            dashPattern: const [8, 3],
                            color: Colors.grey,
                            strokeWidth: 1,
                            child: Container(
                              width: double.infinity,
                              height: 100,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(10),
                                color: Colors.white,
                              ),
                              child: Center(
                                child: Container(
                                  height: 45,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(10),
                                    border:
                                        Border.all(color: Colors.grey.shade300),
                                    color: Colors.white,
                                  ),
                                  child: Padding(
                                    padding: const EdgeInsets.all(2.0),
                                    child: TextButton(
                                      onPressed: () => _pickImage(),
                                      child: Text(
                                        "Add Image",
                                        style: context.textTheme.titleMedium
                                            ?.copyWith(
                                          color:
                                              AppColors.primaryLightTextColor,
                                          fontSize: 12,
                                          fontWeight: FontWeight.w400,
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ),
                  ),
                  const SizedBox(height: 16),
                  // Description TextField for new step
                  IngredientTextField(
                    controller: _descriptionController,
                    hintText: 'Description ...',
                    maxLines: 8,
                    height: 108,
                    onChanged: (value) {
                      // Optional: Update state if needed, but we'll handle on Add/Update
                    },
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            // List of existing steps
            Visibility(
              visible: steps.isNotEmpty,
              child: Expanded(
                child: ListView.builder(
                  itemCount: steps.length,
                  itemBuilder: (context, index) {
                    final step = steps[index];
                    return Container(
                      margin: const EdgeInsets.only(bottom: 20),
                      padding: const EdgeInsets.all(0),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                            color: AppColors.greyBorderColor, width: 1),
                        color: Colors.white,
                        boxShadow: const [
                          BoxShadow(
                            color: Colors.black12,
                            blurRadius: 4,
                            offset: Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Stack(
                        children: [
                          Padding(
                            padding: const EdgeInsets.all(16.0),
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // Image or Placeholder
                                Expanded(
                                  flex: 1,
                                  child: ClipRRect(
                                    borderRadius: BorderRadius.circular(8),
                                    child: CommonImage(
                                      imageSource:
                                          step.mediaFile ?? step.imageUrl,
                                      width: double.infinity,
                                      height: 80,
                                      fit: BoxFit.cover,
                                      placeholder:
                                          AssetsManager.directions_place_holder,
                                      borderRadius: 10.0,
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 16),
                                // Description TextField for editing existing step
                                Expanded(
                                    flex: 2,
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.end,
                                      children: [
                                        CustomText(
                                          text: step.description ?? '',
                                          size: 12,
                                          weight: FontWeight.w400,
                                          color:
                                              AppColors.primaryLightTextColor,
                                        ),
                                        const SizedBox(height: 5),
                                        Row(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            Container(
                                              height: 35,
                                              decoration: BoxDecoration(
                                                color: AppColors.whiteColor,
                                                borderRadius:
                                                    BorderRadius.circular(10.0),
                                                border: Border.all(
                                                  color:
                                                      const Color(0xFFE0E0E0),
                                                  width: 1.5,
                                                ),
                                              ),
                                              child: SizedBox(
                                                child: IconButton(
                                                  icon: const Icon(Icons.delete,
                                                      color: Colors.red),
                                                  onPressed: () {
                                                    stepsNotifier
                                                        .removeStep(index);
                                                  },
                                                ),
                                              ),
                                            ),
                                            const SizedBox(width: 10),
                                            SizedBox(
                                              width: 70,
                                              child: CustomIconTextButton(
                                                iconPath:
                                                    AssetsManager.ic_pencil,
                                                text: 'Edit',
                                                iconColor:
                                                    AppColors.primaryGreyColor,
                                                backgroundColor:
                                                    AppColors.whiteColor,
                                                textColor:
                                                    AppColors.primaryGreyColor,
                                                height: 35,
                                                onTap: () => _editStep(index),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ],
                                    )),
                              ],
                            ),
                          ),
                          Positioned(
                            top: 14,
                            left: 0,
                            child: SizedBox(
                              width: 48,
                              height: 40,
                              child: Stack(
                                alignment: Alignment.center,
                                children: [
                                  SvgPicture.asset(
                                    AssetsManager.steps,
                                    width: 48,
                                    height: 40,
                                    fit: BoxFit.contain,
                                  ),
                                  CustomText(
                                    text: "Step ${index + 1}",
                                    align: TextAlign.center,
                                    color: Theme.of(context)
                                        .scaffoldBackgroundColor,
                                    size: 12,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 35),
          child: CustomButton(
            borderRadius: 10,
            text: 'Next',
            onPressed: () {
              // Validate or save steps
              print("directionsJson ${stepsNotifier.directionsJson}");
              // Assuming there's a provider to handle navigation
              ref.read(stepProvider.notifier).nextStep();
            },
          ),
        ),
      ),
    );
  }
}
