import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter/material.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/core/data/models/recipe_detail_response.dart';
import 'package:mastercookai/core/utils/device_utils.dart';
import 'package:mastercookai/core/utils/validator.dart';
import 'package:mastercookai/core/widgets/custom_drop_down_mobile.dart';
import 'package:mastercookai/core/widgets/custom_input_field.dart';
import 'package:mastercookai/core/widgets/custom_text.dart';
import 'package:mastercookai/presentation/recipe/subview/bottom_sheet_time_picker.dart'
    hide AppColors;
import '../../../../../core/data/models/category_response.dart';
import '../../../../../core/data/models/cuisines_response.dart';
import '../../../../../core/network/app_status.dart';
import '../../../../../core/providers/cuisines_notifier.dart';
import '../../../../../core/providers/recipe/metadata_provider.dart';
import '../../../../../core/providers/recipe/step_Provider.dart';
import '../../../../../core/widgets/custom_button.dart';
import '../../../../../core/widgets/custom_drop_down.dart';
import '../../../../../core/widgets/custom_loading.dart';
import '../../../../cookbook/widgets/custom_title_text.dart';
import '../../../edit_recipe_subview/buildRecipeHeader.dart';
import '../../../subview/custom_ingredent_textfield.dart';

class EditRecipeInfoStep1 extends ConsumerStatefulWidget {
  List<Categories> categories;
  List<Cuisines> cuisines;

  VoidCallback? onUpdateData;
  RecipeDetails recipeDetails;
  TextEditingController recipeNameController;
  TextEditingController recipeDescController;

  EditRecipeInfoStep1({
    super.key,
    required this.categories,
    required this.cuisines,
    this.onUpdateData,
    required this.recipeNameController,
    required this.recipeDescController,
    required this.recipeDetails,
  });

  @override
  ConsumerState<EditRecipeInfoStep1> createState() => _UpdateInfoScreenState();
}

class _UpdateInfoScreenState extends ConsumerState<EditRecipeInfoStep1> {
  // Text controllers
  final _yieldController = TextEditingController();
  final _servingsController = TextEditingController();
  final _prepTimeController = TextEditingController();
  final _cookTimeController = TextEditingController();
  final _totalTimeController = TextEditingController();
  String? category;
  String? cuisine;

  @override
  void initState() {
    super.initState();
    category = widget.recipeDetails.category ?? (widget.categories.isNotEmpty ? widget.categories.first.name : null);
    cuisine = widget.recipeDetails.cuisine ?? (widget.cuisines.isNotEmpty ? widget.cuisines.first.name : null);
    setDefaultData();
  }

  void setDefaultData() {
    final metadata = ref.read(recipeMetadataProvider);
    _yieldController.text = metadata.yieldValue ?? '';
    _servingsController.text = metadata.servings?.toString() ?? '';
    _prepTimeController.text = metadata.prepTime ?? '';
    _cookTimeController.text = metadata.cookTime ?? '';
    _totalTimeController.text = metadata.totalTime ?? '';

    // Parse existing time values to set hours and minutes variables
    final prepTimeData = _parseTimeString(metadata.prepTime);
    prepHours = prepTimeData['hours']!;
    prepMinutes = prepTimeData['minutes']!;

    final cookTimeData = _parseTimeString(metadata.cookTime);
    cookHours = cookTimeData['hours']!;
    cookMinutes = cookTimeData['minutes']!;
  }

  void reset() {
    _yieldController.text = '';
    _servingsController.text = '';
    _prepTimeController.text = '';
    _cookTimeController.text = '';
    _totalTimeController.text = '';
  }

  @override
  void dispose() {
    _yieldController.dispose();
    _servingsController.dispose();
    _prepTimeController.dispose();
    _cookTimeController.dispose();
    _totalTimeController.dispose();
    super.dispose();
  }

  int prepHours = 0, prepMinutes = 0;
  int cookHours = 0, cookMinutes = 0;
  final formKey = GlobalKey<FormState>();

  String _formatTime(int hours, int minutes) {
    return "${hours}h ${minutes}m";
  }

  // Parse time string like "2h 30m" into hours and minutes
  Map<String, int> _parseTimeString(String? timeString) {
    if (timeString == null || timeString.isEmpty) {
      return {'hours': 0, 'minutes': 0};
    }

    // Regular expression to match "Xh Ym" format
    final regex = RegExp(r'(\d+)h\s*(\d+)m');
    final match = regex.firstMatch(timeString);

    if (match != null) {
      return {
        'hours': int.tryParse(match.group(1) ?? '0') ?? 0,
        'minutes': int.tryParse(match.group(2) ?? '0') ?? 0,
      };
    }

    return {'hours': 0, 'minutes': 0};
  }

  String _calculateTotalTime() {
    int totalMinutes =
        (prepHours * 60 + prepMinutes) + (cookHours * 60 + cookMinutes);
    int totalH = totalMinutes ~/ 60;
    int totalM = totalMinutes % 60;

    return _formatTime(totalH, totalM);
  }

  void _updateTotalTimeIfPossible() {
    // Calculate and update total time automatically
    String totalTime = _calculateTotalTime();

    // Update the total time controller and provider
    _totalTimeController.text = totalTime;
    ref.read(recipeMetadataProvider.notifier).updateTotalTime(totalTime);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisSize: MainAxisSize.max,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CustomText(
                text: 'Update info',
                size: 14,
                weight: FontWeight.w500,
                color: AppColors.primaryGreyColor,
              ),
              SizedBox(height: 10),
              Form(
                key: formKey,
                child: CustomInputField(
                  hintText: "Recipe Name",
                  verticalPadding: 8,
                  controller: widget.recipeNameController,
                  maxLines: 1,
                  maxLength: 100,
                  validator: Validator.validateRecipeName,
                  onChanged: (value) {
                    ref.read(recipeMetadataProvider.notifier).updateName(value);
                  },
                ),
              ),
              SizedBox(height: 16.h),
              CustomInputField(
                hintText: "Recipe Description",
                controller: widget.recipeDescController,
                maxLines: 4,
                onChanged: (value) {
                  ref
                      .read(recipeMetadataProvider.notifier)
                      .updateDescription(value);
                },
              ),
              SizedBox(height: 10),
              CustomDropDownMobile<Categories>(
                label: 'Category',
                value: category,
                options: widget.categories,
                onChanged: (val) {
                  setState(() {
                    category = val;
                  });
                  final category1 = widget.categories.firstWhere(
                    (c) => c.name == val,
                    orElse: () => widget.categories.isNotEmpty
                        ? widget.categories[0]
                        : Categories(id: 0, name: 'Unknown'),
                  );
                  ref
                      .read(recipeMetadataProvider.notifier)
                      .updateCategoryId(category1.id);
                },
                getDisplayString: (category) => category.name ?? 'Unknown',
              ),
              SizedBox(height: 10),
              Consumer(
                builder: (context, ref, child) {
                  final cuisinesState = ref.watch(cuisinesNotifierProvider);

                  if (cuisinesState.status == AppStatus.loading ||
                      cuisinesState.status == AppStatus.loadingMore ||
                      cuisinesState.status == AppStatus.error) {
                    return const CustomLoading();
                  }

                  if (cuisinesState.status == AppStatus.success) {
                    final cuisines = cuisinesState.data ?? [];

                    if (cuisines.isEmpty) {
                      return const Text('No cuisines available');
                    }

                    return CustomDropDownMobile<Cuisines>(
                      label: 'Cuisine',
                      value: cuisine,
                      options: cuisines,
                      onChanged: (val) {
                        setState(() {
                          cuisine = val;
                        });
                        final cuisine1 = cuisines.firstWhere(
                          (c) => c.name == val,
                          orElse: () => cuisines[0],
                        );
                        ref
                            .read(recipeMetadataProvider.notifier)
                            .updateCuisineId(cuisine1.id!);
                      },
                      getDisplayString: (cuisine) => cuisine.name ?? 'Unknown',
                    );
                  }

                  return const SizedBox.shrink();
                },
              ),
              SizedBox(height: 10),
              _buildTextField(
                'Yield',
                'Amount/Unit',
                _yieldController,
                (val) {
                  ref.read(recipeMetadataProvider.notifier).updateYield(val);
                },
                InputFormatType.text,
              ),
              _buildTextField(
                'Servings',
                'Servings',
                _servingsController,
                (val) {
                  final servingValue = int.tryParse(val);
                  if (servingValue != null) {
                    ref
                        .read(recipeMetadataProvider.notifier)
                        .updateServings(servingValue);
                  }
                },
                InputFormatType.number,
              ),
              TimePickerFieldBottomSheet(
                label: "Prep time",
                controller: _prepTimeController,
                onTimeSelected: (h, m) {
                  // Update prep time variables
                  prepHours = h;
                  prepMinutes = m;
                  // Format and update prep time
                  String prepTime = _formatTime(h, m);
                  // Update provider and state
                  setState(() {
                    ref
                        .read(recipeMetadataProvider.notifier)
                        .updatePrepTime(prepTime);
                    // Update total time automatically
                    _updateTotalTimeIfPossible();
                  });
                },
              ),
              SizedBox(height: 10),
              TimePickerFieldBottomSheet(
                label: "Cook time",
                controller: _cookTimeController,
                onTimeSelected: (h, m) {
                  // Update cook time variables
                  cookHours = h;
                  cookMinutes = m;

                  // Format and update cook time
                  String cookTime = _formatTime(h, m);

                  // Update provider and state
                  setState(() {
                    ref
                        .read(recipeMetadataProvider.notifier)
                        .updateCookTime(cookTime);
                    // Update total time automatically
                    _updateTotalTimeIfPossible();
                  });
                },
              ),
              SizedBox(height: 10),
              _buildTimeField('Total time', _totalTimeController, (val) {
                ref.read(recipeMetadataProvider.notifier).updateTotalTime(val);
              }),
              SizedBox(height: 10),
            ],
          ),
        ),
      ),
      bottomNavigationBar: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 35),
        child: Row(
          children: [
            Expanded(
              flex: 1,
              child: CustomButton(
                  borderRadius: 10,
                  text: 'Next',
                  onPressed: () {
                    ref.read(stepProvider.notifier).nextStep();
                  }),
            ),
            SizedBox(
              width: 16,
            ),
            Expanded(
                flex: 1,
                child: CustomButton(
                    borderRadius: 10,
                    text: 'Save',
                    onPressed: () {
                      if (formKey.currentState!.validate()) {
                        widget.onUpdateData?.call();
                      }
                    })),
          ],
        ),
      ),
    );
  }

  Widget _buildTextField(
      String title,
      String label,
      TextEditingController controller,
      Function(String)? onChanged,
      InputFormatType formatType) {
    return Row(
      children: [
        Expanded(
          flex: 1,
          child: CustomText(
            text: title,
            color: AppColors.primaryLightTextColor,
            size: 14,
            weight: FontWeight.w400,
            fontFamily: 'Inter',
          ),
        ),
        SizedBox(width: 10.h),
        Expanded(
          flex: 2,
          child: IngredientTextField(
              onChanged: onChanged,
              hintText: label,
              controller: controller,
              height: 40,
              formatType: formatType),
        ),
      ],
    );
  }

  Widget _buildTimeField(String label, TextEditingController controller,
      Function(String)? onChanged) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          flex: 1,
          child: CustomText(
            text: label,
            color: AppColors.primaryLightTextColor,
            size: 14,
            weight: FontWeight.w400,
            fontFamily: 'Inter',
          ),
        ),
        SizedBox(width: 10.w),
        Expanded(
            flex: 2,
            child: Container(
              padding: EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 6,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: CustomText(
                text: controller.text,
                color: AppColors.primaryLightTextColor,
                size: DeviceUtils().isTabletOrIpad(context)
                    ? 14
                    : responsiveFont(14),
                weight: FontWeight.w400,
                fontFamily: 'Inter',
              ),
            )),
      ],
    );
  }
}
