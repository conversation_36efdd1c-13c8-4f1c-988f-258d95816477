import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mastercookai/core/providers/recipe/step_Provider.dart';
import '../../../../../app/assets_manager.dart';
import '../../../../../app/theme/colors.dart';
import '../../../../../core/providers/recipe/ingrident_provider.dart';
import '../../../../../core/widgets/custom_button.dart';
import '../../../../../core/widgets/custom_ccon_text_button.dart';
import '../../../../../core/widgets/custom_text.dart';
import '../../../subview/custom_ingredent_textfield.dart';

Widget editRecipeIngridientStep5(
    {required VoidCallback? onUpdateData,
    required WidgetRef ref,
    required BuildContext context}) {
  return Scaffold(
    body: SafeArea(
      child: Column(
        children: [
          // Header section
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                CustomText(
                    text: 'Update Ingredients',
                    size: 14,
                    weight: FontWeight.w500,
                    color: AppColors.primaryGreyColor),
                Spacer(),
                CustomIconTextButton(
                  iconPath: AssetsManager.ic_plus,
                  text: 'Add row',
                  iconColor: Colors.white,
                  backgroundColor: Colors.blue,
                  textColor: Colors.white,
                  height: 40,
                  onTap: () {
                    ref.read(ingredientsProvider.notifier).addController();
                  },
                ),
              ],
            ),
          ),
          // Scrollable ingredients list
          Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: _buildScrollableIngredientsList(ref),
            ),
          ),
        ],
      ),
    ),
    bottomNavigationBar: SafeArea(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 16),
        child: Row(
          children: [
            Expanded(
              flex: 1,
              child: CustomButton(
                  borderRadius: 10,
                  text: 'Next',
                  onPressed: () {
                    ref.read(stepProvider.notifier).nextStep();
                  }),
            ),
            SizedBox(
              width: 16,
            ),
            Expanded(
                flex: 1,
                child: CustomButton(
                    borderRadius: 10, text: 'Save', onPressed: onUpdateData!)),
          ],
        ),
      ),
    ),
  );
}

// Helper function to build scrollable ingredients list
Widget _buildScrollableIngredientsList(WidgetRef ref) {
  // Watch the provider state to trigger rebuilds when controllers are added/removed
  ref.watch(ingredientsProvider);
  final ingredientsNotifier = ref.watch(ingredientsProvider.notifier);
  final controllers = ingredientsNotifier.controllers;

  return ListView.builder(
    itemCount: controllers.length,
    itemBuilder: (context, index) {
      return Padding(
        padding: EdgeInsets.symmetric(vertical: 8.h),
        child: IngredientTextField(
          controller: controllers[index],
          hintText: 'Add ingredient',
          height: 70.h,
          onChanged: (value) {
            // Update provider on text change
            ingredientsNotifier.setIngredients();
          },
        ),
      );
    },
  );
}
