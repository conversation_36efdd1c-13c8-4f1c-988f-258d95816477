import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mastercookai/core/data/models/recipe_detail_response.dart';
import 'package:mastercookai/core/providers/recipe/step_Provider.dart';
import '../../../../../app/imports/core_imports.dart';
import '../../../../../app/theme/colors.dart';
import '../../../../../core/providers/recipe/media_provider.dart';
import '../../../../../core/widgets/custom_button.dart';
import '../../../../../core/widgets/custom_text.dart';
import '../../../subview/custom_notes_widget.dart';
import '../../../subview/media_picker_grid.dart';

Widget editRecipeNotesStep4(
    {required notes,
    required VoidCallback? onUpdateData,
    required WidgetRef ref}) {
  return Scaffold(
    body: Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CustomText(
            text: 'Update Notes',
            size: 14,
            weight: FontWeight.w500,
            color: AppColors.primaryGreyColor,
          ),
          SizedBox(
            height: 12,
          ),
          CustomNotesWidget(
              title: notes, isCallFromEdit: false, onUpdateData: onUpdateData),
        ],
      ),
    ),
    bottomNavigationBar: Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 35),
      child: Row(
        children: [
          Expanded(
            flex: 1,
            child: CustomButton(
                borderRadius: 10,
                text: 'Next',
                onPressed: () {
                  ref.read(stepProvider.notifier).nextStep();
                }),
          ),
          SizedBox(
            width: 16,
          ),
          Expanded(
              flex: 1,
              child: CustomButton(
                  borderRadius: 10, text: 'Save', onPressed: onUpdateData!)),
        ],
      ),
    ),
  );
}
