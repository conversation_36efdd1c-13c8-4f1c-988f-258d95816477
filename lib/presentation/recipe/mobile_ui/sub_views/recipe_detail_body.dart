import 'package:flutter/cupertino.dart';

import 'package:mastercookai/core/data/models/recipe_response.dart';

import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../../../../app/imports/core_imports.dart';
import '../../../../core/data/models/category_response.dart';
import '../../../../core/data/models/cuisines_response.dart';
import '../../../../core/data/models/recipe_detail_response.dart';
import '../../../../core/network/app_status.dart';
import '../../../../core/providers/categories_notifier.dart';
import '../../../../core/providers/cuisines_notifier.dart';
import '../../../../core/providers/nutrions_notifier.dart';
import '../../../../core/providers/single_recipe_notifier.dart';
import '../../../../core/widgets/dashed_divider.dart';
import '../../detail_subview/build_author_section.dart';
import '../../detail_subview/build_directions_list.dart';
import '../../detail_subview/build_ingredients_section.dart';
import '../../detail_subview/build_notes_section.dart';
import '../../detail_subview/build_nutrition_section.dart';
import '../../detail_subview/build_serving_ideas_section.dart';

import 'Recipe_item_tabbar.dart';
import 'detail_subview_mobile/build_author_section_mobile.dart';
import 'detail_subview_mobile/build_directions_list_mobile.dart';
import 'detail_subview_mobile/build_ingredients_section_mobile.dart';
import 'detail_subview_mobile/build_notes_section_mobile.dart';

class RecipeDetailBody extends ConsumerStatefulWidget {
  final RecipeDetails recipeDetail;
  final int recipeId;
  final int cookbookId;
  final int selectedRecipeId;
  final int selectedCookbookId;
  final List<Recipe> recipesList;
  final String? selectedMediaUrl;

  const RecipeDetailBody(
    this.recipeId,
    this.cookbookId,
    this.recipesList, {
    super.key,
    required this.recipeDetail,
    required this.selectedMediaUrl,
    required this.selectedRecipeId,
    required this.selectedCookbookId,
  });

  @override
  ConsumerState<RecipeDetailBody> createState() => _RecipeHeaderState();
}

class _RecipeHeaderState extends ConsumerState<RecipeDetailBody> {
  var imageUrl = '';

  @override
  void initState() {
    imageUrl = widget.selectedMediaUrl ?? '';
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final categoriesState = ref.watch(categoriesNotifierProvider);
    final cuisinesState = ref.watch(cuisinesNotifierProvider);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        RecipeItemTabbar(),
        Padding(
          padding: const EdgeInsets.only(left: 16.0, right: 16.0, top: 8),
          child: const DashedDivider(thickness: 1),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: _buildTabContent(
            ref.watch(singleRecipeNotifierProvider),
            categoriesState.data,
            cuisinesState.data,
            widget.recipeId,
            widget.cookbookId,
            widget.recipesList,
          ),
        )
      ],
    );
  }

  Widget _buildTabContent(
      AppState<RecipeDetails> recipeState,
      List<Categories>? categoryList,
      List<Cuisines>? cusineList,
      int recipeId,
      int cookbookId,
      List<Recipe> recipesList) {
    final selectedTab = ref.watch(selectedTabProvider);
    switch (selectedTab) {
      case 'Ingredients':
        return buildIngredientsSectionMobile(
            context,
            recipesList,
            widget.selectedCookbookId ?? 0,
            widget.selectedRecipeId ?? 0,
            recipeState.data?.ingredients ?? [],
            recipeState.data ?? RecipeDetails(),
            categoryList,
            cusineList,
            ref: ref); // Author Recipe

      case 'Directions':
        return buildDirectionsListMobile(
            context,
            recipeState.data?.directions ?? [],
            recipeState.data ?? RecipeDetails(),
            categoryList,
            cusineList,
            recipeId,
            cookbookId,
            recipesList,
            ref: ref);

      case 'Nutrition Analysis':
        return NutritionSection(
          recipeDetail: recipeState.data ?? RecipeDetails(),
        );
      case 'Notes': // Notes
        return buildNotesSectionMobile(
          context,
          recipeState.data?.notes ?? '',
          recipeState.data ?? RecipeDetails(),
          categoryList ?? [],
          cusineList,
          recipeId,
          cookbookId,
          recipesList,
          ref: ref,
        );
      case 'Author Info':
        return buildAuthorSectionMobile(
          context,
          recipeState.data?.author ?? '',
          recipeState.data?.authorMediaUrl ?? '',
          recipeState.data?.copyright ?? '',
          recipeState.data?.source ?? '',
          recipeState.data ?? RecipeDetails(),
          categoryList,
          cusineList,
          recipeId,
          cookbookId,
          recipesList,
          ref: ref,
        );
      case 'Other':
        return Column(
          children: [
            SizedBox(height: 16),
            buildServingIdeasSection(
                recipeState.data?.servingIdeas ?? '',
                context,
                recipeState.data?.directions ?? [],
                recipeState.data ?? RecipeDetails(),
                categoryList,
                cusineList,
                recipeId,
                cookbookId,
                recipesList,
                ref: ref),
            SizedBox(height: 16),
            buildWinePairingSection(
                recipeState.data?.wine ?? '',
                context,
                recipeState.data?.directions ?? [],
                recipeState.data ?? RecipeDetails(),
                categoryList,
                cusineList,
                recipeId,
                cookbookId,
                recipesList,
                ref: ref),
          ],
        );
      default:
        return SizedBox.shrink();
    }
  }
}
