import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mastercookai/core/utils/device_utils.dart'; // Assuming this provides device utility functions
import 'package:mastercookai/core/widgets/custom_button.dart';
import 'package:mastercookai/core/widgets/custom_input_field.dart';
import 'package:numberpicker/numberpicker.dart';

import '../../../app/imports/core_imports.dart';
import '../../../core/widgets/custom_text.dart';
import 'non_editable_textfield.dart'; // Assuming this is your custom text field widget

class TimePickerFieldBottomSheet extends StatefulWidget {
  final String label;
  final TextEditingController controller;
  final void Function(int hours, int minutes)? onTimeSelected;

  const TimePickerFieldBottomSheet({
    super.key,
    required this.label,
    required this.controller,
    this.onTimeSelected,
  });

  @override
  State<TimePickerFieldBottomSheet> createState() => _TimePickerFieldState();
}

class _TimePickerFieldState extends State<TimePickerFieldBottomSheet> {
  // No need for LayerLink or OverlayEntry anymore for bottom sheet
  // final LayerLink _layerLink = LayerLink();
  // OverlayEntry? _overlayEntry;

  int selectedHour = 0;
  int selectedMinute = 0;

  void _showBottomTimePicker() {
    // Parse current value from controller if available to pre-fill picker
    final text = widget.controller.text;
    if (text.isNotEmpty) {
      final parts = text.split(' ');
      if (parts.length == 2) {
        try {
          selectedHour = int.parse(parts[0].replaceAll('h', ''));
          selectedMinute = int.parse(parts[1].replaceAll('m', ''));
        } catch (e) {
          // Fallback to default if parsing fails
          selectedHour = 0;
          selectedMinute = 0;
        }
      }
    }

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      // Allows the sheet to take full height if needed
      backgroundColor: Colors.transparent,
      // Make background transparent to show custom shape
      builder: (BuildContext context) {
        return Container(
          width:
              MediaQuery.of(context).size.width > 800 ? 800 : double.infinity,
          decoration: BoxDecoration(
            color: Colors.white, // Background color of the bottom sheet content
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20.r),
              topRight: Radius.circular(20.r),
            ),
          ),
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 20.h),
          child: Column(
            mainAxisSize: MainAxisSize.min, // Make column take minimum space
            children: [
              CustomText(
                text: "Select ${widget.label}",
                color: Colors.black87,
                size: 20,
                weight: FontWeight.w600,
                fontFamily: 'Inter',
              ),
              SizedBox(height: 20.h),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Hour picker
                  StatefulBuilder(
                    builder: (context, setStateHr) {
                      return NumberPicker(
                        value: selectedHour,
                        minValue: 0,
                        maxValue: 23,
                        itemHeight: 50,
                        textStyle: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                        selectedTextStyle: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color:
                              AppColors.primaryColor, // Use your primary color
                        ),
                        onChanged: (value) =>
                            setStateHr(() => selectedHour = value),
                        axis: Axis.vertical,
                        decoration: BoxDecoration(
                          border: Border.symmetric(
                            vertical: BorderSide(
                                color: Colors.grey.shade300, width: 0.5),
                          ),
                        ),
                      );
                    },
                  ),
                  SizedBox(width: 8.w),
                  CustomText(
                    text: "hr",
                    color: Colors.black87,
                    size: 16,
                    weight: FontWeight.w500,
                    fontFamily: 'Inter',
                  ),

                  SizedBox(width: 16.w),
                  // Minute picker
                  StatefulBuilder(
                    builder: (context, setStateMin) {
                      return NumberPicker(
                        value: selectedMinute,
                        minValue: 0,
                        maxValue: 59,
                        step: 5,
                        // Allows picking minutes in steps of 5
                        itemHeight: 50,
                        textStyle: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                        selectedTextStyle: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color:
                              AppColors.primaryColor, // Use your primary color
                        ),
                        onChanged: (value) =>
                            setStateMin(() => selectedMinute = value),
                        axis: Axis.vertical,
                        decoration: BoxDecoration(
                          border: Border.symmetric(
                            vertical: BorderSide(
                                color: Colors.grey.shade300, width: 0.5),
                          ),
                        ),
                      );
                    },
                  ),
                  SizedBox(width: 8.w),
                  CustomText(
                    text: "min",
                    color: Colors.black87,
                    size: 16,
                    weight: FontWeight.w500,
                    fontFamily: 'Inter',
                  ),
                ],
              ),
              SizedBox(height: 30.h),
              CustomButton(
                  borderRadius: 10,
                  width: 100,
                  text: 'Done',
                  onPressed: () {
                    widget.controller.text =
                        "${selectedHour}h ${selectedMinute}m";
                    widget.onTimeSelected?.call(selectedHour, selectedMinute);
                    Navigator.pop(context); // Close the
                  }),
              SizedBox(height: 10.h),
            ],
          ),
        );
      },
    );
  }

  @override
  void dispose() {
    // No need to remove overlay entry as showModalBottomSheet manages its own lifecycle
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return getDeviceType(context).name == 'mobile'
        ? Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Expanded(
                flex: 1,
                child: CustomText(
                  text: widget.label,
                  color: AppColors.primaryLightTextColor,
                  size: getDeviceType(context).name == 'mobile'
                      ? 14
                      : DeviceUtils().isTabletOrIpad(context)
                          ? 12
                          : responsiveFont(12),
                  weight: FontWeight.w400,
                  fontFamily: 'Inter',
                ),
              ),
              SizedBox(width: 16.w),
              Expanded(
                flex: 2,
                child: SizedBox(
                  height: 45, // Match text field height
                  child: GestureDetector(
                    onTap:
                        _showBottomTimePicker, // Call the bottom sheet function
                    behavior: HitTestBehavior.translucent,
                    child: AbsorbPointer(
                      child: NonEditableTextField(
                        hintText: "Select",
                        controller: widget.controller,
                        //  editable: false,
                        height: 60.h,
                        hideCross: true,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          )
        : Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              SizedBox(
                width: DeviceUtils().isTabletOrIpad(context) ? 70 : 170.w,
                child: CustomText(
                  text: widget.label,
                  color: Colors.grey,
                  size: DeviceUtils().isTabletOrIpad(context)
                      ? 12
                      : responsiveFont(12),
                  weight: FontWeight.w600,
                  fontFamily: 'Inter',
                ),
              ),
              SizedBox(width: 16.w),
              Expanded(
                child: SizedBox(
                  height: 60.h, // Match text field height
                  child: GestureDetector(
                    onTap:
                        _showBottomTimePicker, // Call the bottom sheet function
                    behavior: HitTestBehavior.translucent,
                    child: AbsorbPointer(
                      child: NonEditableTextField(
                        hintText: "Select",
                        controller: widget.controller,
                        height: 60.h,
                        hideCross: true,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          );
  }
}
