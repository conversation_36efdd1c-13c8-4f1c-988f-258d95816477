import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/core/widgets/custom_button.dart';
import '../../../../core/helpers/media_picker_service.dart';
import '../../../core/data/models/direction_step.dart';
import '../../../core/providers/recipe/directions_provider.dart';
import '../../../core/widgets/common_image.dart';
import '../../../core/widgets/image_cropper/image_cropper.dart';
import '../../cookbook/widgets/custom_title_text.dart';
import 'circle_icon.dart';
import 'custom_ingredent_textfield.dart';
import 'custom_sub_media_widget.dart';

class CustomDirectionsWidgets extends ConsumerStatefulWidget {
  final bool? isCallFromEdit;
  final bool? callFromClipper;
  final VoidCallback? onUpdateData;

  const CustomDirectionsWidgets({
    super.key,
    this.isCallFromEdit = false,
    this.onUpdateData,
    this.callFromClipper = false,
  });

  @override
  ConsumerState<CustomDirectionsWidgets> createState() =>
      _CustomDirectionsWidgetState();
}

class _CustomDirectionsWidgetState extends ConsumerState<CustomDirectionsWidgets> {
  final List<TextEditingController> _descriptionControllers = [];
  List<DirectionStep>? directions; // Nullable, no late

  @override
  void initState() {
    super.initState();
    // Initialize directions immediately
    directions = ref.read(directionStepsProvider);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Only add default steps if directions are null or empty and not from clipper
      if ((directions == null || directions!.isEmpty) && !widget.callFromClipper!) {
        ref.read(directionStepsProvider.notifier).addStep();
        ref.read(directionStepsProvider.notifier).addStep();
        ref.read(directionStepsProvider.notifier).addStep();
        // Initialize controllers for default steps
        _descriptionControllers.addAll(
          List.generate(3, (_) => TextEditingController(text: "")),
        );
      } else if (directions != null && directions!.isNotEmpty) {
        // Initialize controllers with existing data
        for (var step in directions!) {
          _descriptionControllers.add(TextEditingController(text: step.description ?? ""));
        }
      }
      setState(() {}); // Trigger rebuild to sync UI
    });
  }

  @override
  void dispose() {
    for (var controller in _descriptionControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  void _addStep() {
    ref.read(directionStepsProvider.notifier).addStep();
    setState(() {
      _descriptionControllers.add(TextEditingController(text: ""));
      print("Added new step, total steps: ${_descriptionControllers.length}");
    });
  }

  void _deleteStep(int index) {
    if (_descriptionControllers.length <= 2) return;
    setState(() {
      _descriptionControllers[index].dispose();
      _descriptionControllers.removeAt(index);
      print("Deleted step at index: $index");
    });
    ref.read(directionStepsProvider.notifier).removeStep(index);
  }

  void _deleteMedia(int index) {
    setState(() {
      ref.read(directionStepsProvider.notifier).removeMediaFile(index);
      print("Deleted media for step $index");
    });
  }

  void _editMedia(int index) async {
    final pickedFile = await MediaPickerService.pickSingleImage();
    if (pickedFile != null && mounted) {
      final croppedImageFile = await Navigator.of(context).push<File?>(
        MaterialPageRoute(
          builder: (context) => ImageCropper(
            pickedImage: pickedFile,
            showCropPresets: true,
            showGridLines: false,
            enableFreeformCrop: true,
          ),
        ),
      );

      if (croppedImageFile != null && mounted) {
        setState(() {
          ref.read(directionStepsProvider.notifier).updateStep(
                index,
                mediaFile: croppedImageFile,
                mediaFileName: croppedImageFile.path.split('/').last,
                imageUrl: '',
              );
          print("Edited image for step $index: ${croppedImageFile.path}");
        });
      } else {
        print("Cropped image is null or user canceled.");
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // Watch directionStepsProvider to update directions reactively
    directions = ref.watch(directionStepsProvider);
    print("Building with directions: ${directions?.length ?? 0} steps");

    // Handle null or empty directions
    if (directions == null || directions!.isEmpty) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const CustomeTitleText(title: 'Directions (0)'),
          SizedBox(height: 20.h),
          Text(
            widget.callFromClipper!
                ? 'No directions available from clipper'
                : 'No directions added',
            style: context.textTheme.bodyMedium?.copyWith(
              color: AppColors.primaryGreyColor,
              fontSize: 16.sp,
            ),
          ),
          SizedBox(height: 12.h),
          Center(
            child: CustomButton(
              text: "Add more step",
              onPressed: _addStep,
              fontSize: 14,
              width: 140,
              color: AppColors.lightestGreyColor,
              textColor: Colors.black,
            ),
          ),
        ],
      );
    }

    // Sync controllers with direction steps
    if (_descriptionControllers.length != directions!.length) {
      setState(() {
        while (_descriptionControllers.length < directions!.length) {
          _descriptionControllers.add(TextEditingController(
            text: widget.isCallFromEdit! || widget.callFromClipper!
                ? directions![_descriptionControllers.length].description ?? ""
                : "",
          ));
          print("Added controller for step ${_descriptionControllers.length}");
        }

        while (_descriptionControllers.length > directions!.length) {
          _descriptionControllers.last.dispose();
          _descriptionControllers.removeLast();
          print(
              "Removed excess controller, total: ${_descriptionControllers.length}");
        }
      });
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        CustomeTitleText(title: 'Directions (${directions!.length})'),
        SizedBox(height: 20.h),
        ScrollConfiguration(
          behavior: const ScrollBehavior()
              .copyWith(overscroll: false, scrollbars: false),
          child: ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: directions!.length,
            itemBuilder: (context, index) {
              final step = directions![index];
              return Container(
                key: ValueKey(index),
                margin: const EdgeInsets.symmetric(vertical: 12),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Image area
                    Stack(
                      children: [
                        if (step.mediaFile != null || step.imageUrl.isNotEmpty)
                          Padding(
                            padding: const EdgeInsets.all(1.0),
                            child: CommonImage(
                              imageSource: step.mediaFile ?? step.imageUrl,
                              width: 250,
                              height: 150,
                              fit: BoxFit.cover,
                              placeholder:
                                  AssetsManager.directions_place_holder,
                              borderRadius: 10.0,
                            ),
                          )
                        else
                          Container(
                            margin: const EdgeInsets.all(12),
                            width: 200,
                            height: 150,
                            child: CustomPaint(
                              painter: DottedBorderPainter(),
                              child: Container(
                                alignment: Alignment.center,
                                child: Container(
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(10),
                                    border:
                                        Border.all(color: Colors.grey.shade300),
                                    color: Colors.white,
                                  ),
                                  child: Padding(
                                    padding: const EdgeInsets.all(2.0),
                                    child: TextButton(
                                      onPressed: () => _editMedia(index),
                                      child: Text(
                                        "Add Image",
                                        style: context.textTheme.titleMedium
                                            ?.copyWith(
                                          color:
                                              AppColors.primaryLightTextColor,
                                          fontSize: 12,
                                          fontWeight: FontWeight.w400,
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        if (step.mediaFile != null || step.imageUrl.isNotEmpty)
                          Positioned(
                            bottom: 10,
                            left: 10,
                            right: 10,
                            child: Container(
                              decoration: BoxDecoration(
                                color: Colors.black54.withValues(alpha: 0.2),
                                borderRadius: BorderRadius.only(
                                  bottomLeft: Radius.circular(10.sp),
                                  bottomRight: Radius.circular(10.sp),
                                ),
                              ),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceEvenly,
                                children: [
                                  circleIcon(AssetsManager.edit_white,
                                      () => _deleteMedia(index)),
                                  circleIcon(AssetsManager.dlt_white,
                                      () => _editMedia(index)),
                                ],
                              ),
                            ),
                          ),
                      ],
                    ),
                    // TextField
                    Expanded(
                      child: Padding(
                        padding: EdgeInsets.all(10.sp),
                        child: ScrollConfiguration(
                          behavior: const ScrollBehavior()
                              .copyWith(overscroll: false, scrollbars: false),
                          child: IngredientTextField(
                            controller: _descriptionControllers[index],
                            hintText: 'Description ...',
                            maxLines: 8,
                            height: 250.h,
                            onChanged: (value) {
                              ref
                                  .read(directionStepsProvider.notifier)
                                  .updateStep(
                                    index,
                                    description: value,
                                  );
                              print(
                                  "Updated description for step $index: $value");
                            },
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ),
        SizedBox(height: 12.h),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CustomButton(
              text: "Add more step",
              onPressed: _addStep,
              fontSize: 14,
              width: 140,
              color: AppColors.lightestGreyColor,
              textColor: Colors.black,
            ),
            SizedBox(width: 20.h),
            Visibility(
              visible: widget.isCallFromEdit ?? false,
              child: CustomButton(
                text: "Save Changes",
                width: 140,
                fontSize: 14,
                onPressed: () {
                  widget.onUpdateData?.call();
                  // print("Save Changes pressed, directions: ${directions?.length ?? 0}");
                },
              ),
            ),
          ],
        ),
        SizedBox(height: 12.h),
      ],
    );
  }
}
