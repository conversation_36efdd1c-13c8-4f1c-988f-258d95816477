import 'package:mastercookai/core/utils/device_utils.dart';

import '../../../../../app/imports/packages_imports.dart';
import '../../../app/theme/colors.dart';
import '../../../core/providers/recipe/metadata_provider.dart';
import '../../../core/widgets/custom_text.dart';
import '../../cookbook/widgets/custom_title_text.dart';
import 'custom_ingredent_textfield.dart';

class CustomServingWidget extends ConsumerStatefulWidget {
  final String? servingIdeas;
  final bool? isCallFromAdd;
  CustomServingWidget({super.key,   this.servingIdeas,this.isCallFromAdd =false});

  @override
  ConsumerState<CustomServingWidget> createState() =>
      _CustomServingWidgetState();
}

class _CustomServingWidgetState extends ConsumerState<CustomServingWidget> {

  late TextEditingController descController;

  @override
  void initState() {
    super.initState();
    final metadata = ref.read(recipeMetadataProvider);
    // Initialize noteController with value from notesProvider or widget.notes
    descController = TextEditingController(text: widget.isCallFromAdd??false?metadata.servingIdeas: widget.servingIdeas ?? '',);
  }


  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
         getDeviceType(context).name=='mobile'?CustomText(text: 'Serving ideas',
             size: 14, weight: FontWeight.w500, color: AppColors.primaryGreyColor): CustomeTitleText(title: 'Serving ideas'),
          SizedBox(height: 20.h),
          IngredientTextField(
            onChanged: (val){
              ref.read(recipeMetadataProvider.notifier).updateServingIdeas(val);
            },
            hintText: "Description",
            controller: descController,
            maxLines: 7,
            height: 200.h,
          ),

          // CustomButton(text: "Save changes", onPressed: (){} , width: 210.w, fontSize: 18.sp,),
        ],
      ),
    );
  }
}
