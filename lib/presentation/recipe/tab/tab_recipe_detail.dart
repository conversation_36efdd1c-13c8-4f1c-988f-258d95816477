import 'package:flutter/cupertino.dart';
import 'package:mastercookai/core/data/models/category_response.dart';
import 'package:mastercookai/core/data/models/cuisines_response.dart';
import 'package:mastercookai/core/data/models/recipe_detail_response.dart';
import 'package:mastercookai/core/data/models/recipe_response.dart';
import 'package:mastercookai/core/network/app_status.dart';
import '../../../../app/assets_manager.dart';
import '../../../../app/imports/packages_imports.dart';
import '../../../../app/theme/colors.dart';
import '../../../../core/providers/categories_notifier.dart';
import '../../../../core/providers/cuisines_notifier.dart';
import '../../../../core/providers/recipe_notifier.dart';
import '../../../../core/providers/single_recipe_notifier.dart';
import '../../../../core/utils/Utils.dart';
import '../../../../core/widgets/common_image.dart';
import '../../../../core/widgets/no_data_widget.dart';
import '../../../../core/widgets/video_player_widget.dart';
import '../../../core/providers/nutrions_notifier.dart';
import '../../../core/providers/tab_index_notifier.dart';
import '../../shimer/recipe_detail_shimmer.dart';
import '../../shimer/recipe_detail_tab_shimmer.dart';
import '../detail_subview/build_author_section.dart';
import '../detail_subview/build_directions_list.dart';
import '../detail_subview/build_ingredients_section.dart';
import '../detail_subview/build_notes_section.dart';
import '../detail_subview/build_recipe_header.dart';
import '../detail_subview/build_recipe_metadata.dart';
import '../detail_subview/build_serving_ideas_section.dart';
import '../detail_subview/build_similar_recipes_list.dart';
import 'custom_Tabbar.dart';

class TabRecipeDetail extends ConsumerStatefulWidget {
  final int recipeId;
  final int cookbookId;
  final int selectedRecipeId;
  final int selectedCookbookId;
  final List<Recipe> recipesList;

  const TabRecipeDetail({
    Key? key,
    required this.recipeId,
    required this.cookbookId,
    required this.recipesList,
    required this.selectedRecipeId,
    required this.selectedCookbookId,
  }) : super(key: key);

  @override
  ConsumerState<TabRecipeDetail> createState() => _TabRecipeDetailState();
}

class _TabRecipeDetailState extends ConsumerState<TabRecipeDetail> {
  String? selectedMediaUrl;
  dynamic selectedMediaFile; // Adjust type if known

  @override
  Widget build(BuildContext context) {
    final cuisinesState = ref.watch(cuisinesNotifierProvider);
    final recipeListState = ref.watch(recipeNotifierProvider);
    final recipeState = ref.watch(singleRecipeNotifierProvider);
    final categoriesState = ref.watch(categoriesNotifierProvider);
    final selectedTabIndex = ref.watch(tabIndexNotifierProvider);

    return (recipeState.status == AppStatus.loading)
        ? RecipeDetailTabShimmer()
        : (recipeListState.data == null || recipeListState.data!.isEmpty)
            ? Center(
                child: NoDataWidget(
                  title: "No Recipe Detail Found",
                  subtitle:
                      "Try searching again or select a different recipe to view the details.",
                  width: 250,
                  height: 250,
                ),
              )
            : Consumer(
                builder: (context, ref, _) {
                  if (recipeListState.status == AppStatus.loading) {
                    return RecipeDetailTabShimmer();
                  }

                  if (recipeListState.status == AppStatus.error ||
                      recipeListState.data == null) {
                    return NoDataWidget(
                      title: "No Recipe Detail Found",
                      subtitle:
                          "Try searching again or select a different recipe to view the details.",
                      width: 250,
                      height: 250,
                    );
                  }

                  RecipeDetails? recipeDetail = recipeState.data;

                  //  return SizedBox.shrink();

                  return Container(
                    margin: const EdgeInsets.all(24),
                    decoration: BoxDecoration(
                      color: AppColors.secondaryColor,
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.05),
                          blurRadius: 8,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: SingleChildScrollView(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          children: [
                            RecipeHeader(
                              recipeDetail: recipeDetail ?? RecipeDetails(),
                            ),
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Expanded(
                                  flex: 3,
                                  child: Column(
                                    children: [
                                      Card(
                                        color: Colors.white,
                                        child: ClipRRect(
                                          borderRadius:
                                              BorderRadius.circular(12),
                                          child: selectedMediaUrl != null &&
                                                  Utils()
                                                      .isVideo(selectedMediaUrl)
                                              ? SizedBox(
                                                  height: 350,
                                                  width: MediaQuery.of(context)
                                                      .size
                                                      .width,
                                                  child: AspectRatio(
                                                    aspectRatio: 16 / 9,
                                                    child: VideoPlayerWidget(
                                                      mediaUrl:
                                                          selectedMediaUrl,
                                                      thumbnailPath: recipeDetail
                                                              ?.recipeThumbnailFileUrl ??
                                                          '',
                                                      height: 360,
                                                    ),
                                                  ),
                                                )
                                              : CommonImage(
                                                  imageSource:
                                                      selectedMediaUrl ?? '',
                                                  height: 360,
                                                  width: MediaQuery.of(context)
                                                      .size
                                                      .width,
                                                  fit: BoxFit.cover,
                                                  placeholder: AssetsManager
                                                      .recipe_place_holder,
                                                ),
                                        ),
                                      ),
                                      SizedBox(height: 30.h),
                                      if (recipeDetail?.recipeMedia != null &&
                                          (recipeDetail
                                                  ?.recipeMedia?.isNotEmpty ??
                                              false))
                                        SimilarRecipesWidget(
                                          similarRecipes:
                                              recipeDetail?.recipeMedia ?? [],
                                          recipeThumbnailFileUrl: recipeDetail
                                                  ?.recipeThumbnailFileUrl ??
                                              '',
                                          onImageSelected:
                                              (String newImageUrl) {
                                            setState(() {
                                              selectedMediaUrl = newImageUrl;
                                              selectedMediaFile = null;
                                            });
                                          },
                                        ),
                                    ],
                                  ),
                                ),
                                SizedBox(width: 30.w),
                                Expanded(
                                  flex: 2,
                                  child: buildRecipeMetadata(
                                    context,
                                    recipeDetail ?? RecipeDetails(),
                                    recipeDetail ?? RecipeDetails(),
                                    categoriesState.data,
                                    cuisinesState.data,
                                    widget.recipeId,
                                    widget.cookbookId,
                                    widget.recipesList,
                                    ref: ref,
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(height: 20.h),
                            CustomTabBar(
                              selectedTabIndex: selectedTabIndex,
                              onTabSelected: (index) {
                                setState(() {
                                  ref
                                      .read(tabIndexNotifierProvider.notifier)
                                      .selectTab(index);
                                  //  _selectedTabIndex = index;
                                });
                              },
                            ),
                            SizedBox(height: 20.h),
                            _buildTabContent(
                              recipeState,
                              categoriesState.data,
                              cuisinesState.data,
                              widget.recipeId,
                              widget.cookbookId,
                              widget.recipesList,
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              );
  }

  Widget _buildTabContent(
      AppState<RecipeDetails> recipeState,
      List<Categories>? categoryList,
      List<Cuisines>? cusineList,
      int recipeId,
      int cookbookId,
      List<Recipe> recipesList) {
    switch (ref.read(tabIndexNotifierProvider)) {
      case 0:
        return buildIngredientsSection(
            context,
            recipesList,
            widget.selectedCookbookId ?? 0,
            widget.selectedRecipeId ?? 0,
            recipeState.data?.ingredients ?? [],
            recipeState.data ?? RecipeDetails(),
            categoryList,
            cusineList,
            ref: ref); // Author Recipe

      case 1:
        return buildDirectionsList(
            context,
            recipeState.data?.directions ?? [],
            recipeState.data ?? RecipeDetails(),
            categoryList,
            cusineList,
            recipeId,
            cookbookId,
            recipesList,
            ref: ref);

      case 2: // Notes
        return buildNotesSection(
          context,
          recipeState.data?.notes ?? '',
          recipeState.data ?? RecipeDetails(),
          categoryList ?? [],
          cusineList,
          recipeId,
          cookbookId,
          recipesList,
          ref: ref,
        );
      case 3:
        return buildAuthorSection(
          context,
          recipeState.data?.author ?? '',
          recipeState.data?.authorMediaUrl ?? '',
          recipeState.data?.copyright ?? '',
          recipeState.data?.source ?? '',
          recipeState.data ?? RecipeDetails(),
          categoryList,
          cusineList,
          recipeId,
          cookbookId,
          recipesList,
          ref: ref,
        );
      case 4:
        return Column(
          children: [
            SizedBox(height: 40.h),
            buildServingIdeasSection(
                recipeState.data?.servingIdeas ?? '',
                context,
                recipeState.data?.directions ?? [],
                recipeState.data ?? RecipeDetails(),
                categoryList,
                cusineList,
                recipeId,
                cookbookId,
                recipesList,
                ref: ref),
            SizedBox(height: 40.h),
            buildWinePairingSection(
                recipeState.data?.wine ?? '',
                context,
                recipeState.data?.directions ?? [],
                recipeState.data ?? RecipeDetails(),
                categoryList,
                cusineList,
                recipeId,
                cookbookId,
                recipesList,
                ref: ref),
          ],
        );
      default:
        return SizedBox.shrink();
    }
  }
}
