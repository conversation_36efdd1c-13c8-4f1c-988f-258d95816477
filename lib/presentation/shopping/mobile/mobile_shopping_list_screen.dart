import 'dart:async';
import 'package:mastercookai/core/providers/shopping/shopping_notifier.dart';
import 'package:mastercookai/core/utils/device_utils.dart';
import 'package:mastercookai/presentation/shopping/mobile/sub_view/pantry_view.dart';
import 'package:mastercookai/presentation/shopping/mobile/sub_view/shopping_view.dart';
import '../../../../app/imports/core_imports.dart';
import '../../../../app/imports/packages_imports.dart';
import '../../../../core/widgets/custom_appbar_mobile.dart';
import '../../../../core/providers/shopping/pantry_notifier.dart';
import 'package:mastercookai/core/data/models/view_type.dart';
import 'package:mastercookai/presentation/shopping/mobile/sub_views/shopping_pantry_bottom_sheet_content.dart';

final viewTypeProvider = StateProvider<ViewType>((ref) => ViewType.grid);

class MobileShoppingListScreen extends ConsumerStatefulWidget {
  const MobileShoppingListScreen({super.key});

  @override
  ConsumerState<MobileShoppingListScreen> createState() =>
      _MobileShoppingListScreenState();
}

class _MobileShoppingListScreenState
    extends ConsumerState<MobileShoppingListScreen>
    with SingleTickerProviderStateMixin {
  final PageController _tabPageController = PageController();
  final TextEditingController searchController = TextEditingController();
  String selectedTab = 'Shopping';
  int currentTabIndex = 0;
  Timer? _debounceTimer;
  String _currentSearchQuery = '';
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Future.delayed(const Duration(milliseconds: 100), () {
        if (mounted) {
          ref
              .read(shoppingNotifierProvider.notifier)
              .fetchShoppingLists(context: context);
          ref.read(pantryNotifierProvider.notifier).fetchPantryLists();
        }
      });
    });
  }

  @override
  void dispose() {
    _tabPageController.dispose();
    searchController.dispose();
    _debounceTimer?.cancel();
    _tabController.dispose();
    super.dispose();
  }

  void _onSearchChanged(String query) {
    _debounceTimer?.cancel();
    _debounceTimer = Timer(const Duration(milliseconds: 500), () {
      if (query != _currentSearchQuery) {
        setState(() {
          _currentSearchQuery = query;
        });
        _performSearch(query);
      }
    });
  }

  void _performSearch(String query) {
    if (selectedTab == 'Shopping') {
      ref.read(shoppingNotifierProvider.notifier).fetchShoppingLists(
            context: context,
            search: query.isEmpty ? null : query,
          );
    } else {
      ref.read(pantryNotifierProvider.notifier).fetchPantryLists(
            search: query.isEmpty ? null : query,
          );
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isHighRes = screenSize.width > 2000;
    final currentViewType = ref.watch(viewTypeProvider);

    return Scaffold(
      appBar: CustomAppbarMobile(
        title: 'Shopping List',
        isLeading: false,
        onSearchChanged: (val) {
          _onSearchChanged(val);
        },
        onViewTypePressed: () {
          ref.read(viewTypeProvider.notifier).state =
              currentViewType == ViewType.grid
                  ? ViewType.list
                  : ViewType.grid;
        },
      ),
      
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 0.0, vertical: 0.0),
            child: Container(
              height: 45,
              decoration: BoxDecoration(
                color: Colors.grey[200],
                borderRadius: BorderRadius.circular(0.0),
              ),
              child: Padding(
                padding: const EdgeInsets.all(4.0),
                child: TabBar(
                  controller: _tabController,
                  indicator: UnderlineTabIndicator(
                    borderRadius: BorderRadius.circular(25.0),
                    borderSide: BorderSide(
                      width: 3.0,  // Height of the indicator
                      color: Colors.red,
                    ),
                  ),
                  indicatorWeight: 3.0,  // Same as borderSide width
                  indicatorSize: TabBarIndicatorSize.tab,
                  labelColor:Colors.red,
                  unselectedLabelColor: Colors.black,
                  overlayColor: WidgetStateProperty.all(Colors.transparent),
                  splashFactory: NoSplash.splashFactory,
                  dividerColor: Colors.transparent,
                  tabs: const [
                    Tab(text: "Shopping"),
                    Tab(text: "Pantry"),
                  ],
                  onTap: (index) {
                    setState(() {
                      currentTabIndex = index;
                      selectedTab = index == 0 ? 'Shopping' : 'Pantry';
                    });
                  },
                ),
              ),
            ),
          ),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                ShoppingView(
                    screenSize: screenSize,
                    isHighRes: isHighRes,
                    searchQuery:
                        _currentSearchQuery.isEmpty ? null : _currentSearchQuery,
                    selectedTab: selectedTab),
                PantryView(
                    screenSize: screenSize,
                    isHighRes: isHighRes,
                    searchQuery:
                        _currentSearchQuery.isEmpty ? null : _currentSearchQuery,
                    selectedTab: selectedTab),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
