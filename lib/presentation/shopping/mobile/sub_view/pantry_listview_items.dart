import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/core/data/models/pantry.dart';
import 'package:mastercookai/core/providers/shopping/pantry_notifier.dart';
import 'package:mastercookai/core/widgets/common_image.dart';
import 'package:timeago/timeago.dart' as timeago;

import '../../../../core/network/app_status.dart';
import '../../../../core/utils/Utils.dart';
import '../../../../core/widgets/custom_hover_menu.dart';

class PantryListviewItems extends ConsumerWidget {
  final Pantry pantry;
  final bool isHighRes;
  final VoidCallback onTap;
  final String selectedTab;
  final int pantryIndex;

  const PantryListviewItems({
    super.key,
    required this.pantry,
    required this.isHighRes,
    required this.onTap,
    required this.selectedTab,
    required this.pantryIndex,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final pantryListNotifier = ref.read(pantryNotifierProvider.notifier);
    final pantryListState = ref.watch(pantryNotifierProvider);

    final isDeleting = pantryListState.status == AppStatus.loading;

    return GestureDetector(
      onTap: () {
        if (!isDeleting) {
         context.go(
              '/shopping/mobile_shopping_list_detail_screen?tab=$selectedTab&index=$pantryIndex');
        }
      },
      child: SizedBox(
          height: 100,
          child: Card(
            elevation: 3,
            color: context.theme.cardColor,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Image Section
                  ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: CommonImage(
                      imageSource: pantry.imageUrl,
                      placeholder: AssetsManager.shoppingDummy,
                      height: 90,
                      width: 90,
                      fit: BoxFit.cover,
                    ),
                  ),
                  const SizedBox(width: 12),
                  // Text and Action Section
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // Title and icons row
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Expanded(
                              child: Text(
                                pantry.title,
                                style: context.theme.textTheme.bodyMedium!
                                    .copyWith(
                                  color: AppColors.primaryGreyColor,
                                  fontSize: 16,
                                  fontWeight: FontWeight.w400,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            CustomHoverMenu(
                              items: ['Edit', 'Delete'],
                              itemIcons: [Icons.edit, Icons.delete],
                              onItemSelected: (value) async {
                                if (value == 'Edit') {
                                  onTap();
                                } else if (value == 'Delete') {
                                  if (!isDeleting) {
                                    final bool? confirmed =
                                        await Utils().showCommonConfirmDialog(
                                      context: context,
                                      title: 'Delete Pantry',
                                      subtitle:
                                          'Are you sure you want to delete "${pantry.title}" pantry?',
                                      confirmText: 'Delete',
                                      cancelText: 'Cancel',
                                    );

                                    if (confirmed != true || !context.mounted) {
                                      return;
                                    }

                                    final result = await pantryListNotifier
                                        .deletePantryList(
                                            context, pantry.id.toString());

                                    if (!context.mounted) {
                                      return;
                                    }

                                    if (result.$1 == AppStatus.success) {
                                      pantryListNotifier.fetchPantryLists(
                                          context: context);
                                    }
                                  }
                                }
                              },
                              menuWidth: 140.0,
                              menuTitle: 'Show Menu',
                              triggerIcon: Icons.more_vert_outlined,
                            ),
                          ],
                        ),
                        const SizedBox(height: 30),
                        // Footer: recipes count and creation date
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            Text(
                              '${pantry.recipeCount} products',
                              style: const TextStyle(
                                fontSize: 12,
                                color: Colors.grey,
                              ),
                            ),
                            Text(
                              'Created : ${timeago.format(DateTime.parse(pantry.createdDate), locale: 'en_short')}',
                              style: const TextStyle(
                                fontSize: 10,
                                color: Colors.grey,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          )),
    );
  }
}
