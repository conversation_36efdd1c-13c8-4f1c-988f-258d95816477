import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/core/providers/shopping/pantry_notifier.dart';
import 'package:mastercookai/core/providers/shopping/page_state_provider.dart';
import 'package:mastercookai/presentation/shopping/mobile/sub_view/pantry_card.dart';
import 'package:mastercookai/presentation/shopping/mobile/sub_views/shopping_pantry_bottom_sheet_content.dart';
import '../../../../core/data/models/pantry.dart';
import '../../../../core/network/app_status.dart';
import '../../../../core/widgets/no_data_widget.dart';
import '../../../../core/widgets/paginated_responsive_grid_list.dart';
import '../../../cookbook/mobile/sub_views/import_create_card_mobile.dart';
import '../../../cookbook/mobile/sub_views/import_create_card_mobile_list.dart';
import '../../../shimer/cookbook_shimmer.dart';
import 'package:mastercookai/presentation/shopping/mobile/mobile_shopping_list_screen.dart';
import 'package:mastercookai/core/data/models/view_type.dart';
import 'pantry_listview_items.dart';

class PantryView extends ConsumerStatefulWidget {
  final Size screenSize;
  final bool isHighRes;
  final String? searchQuery;
  final String selectedTab;

  PantryView({
    Key? key,
    required this.screenSize,
    required this.isHighRes,
    this.searchQuery,
    required this.selectedTab,
  }) : super(key: key);

  @override
  ConsumerState<PantryView> createState() => _PantryViewState();
}

class _PantryViewState extends ConsumerState<PantryView>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  Widget _buildPantryItem(dynamic item, int index, ViewType currentViewType) {
    if (index == 0) {
      // Create card
      return currentViewType == ViewType.grid
          ? ImportCreateCardMobile(
              importText: "Import Pantry",
              title: "Add New Pantry",
              isRecipe: false,
              onImport: () {},
              onCreate: () {
                showModalBottomSheet(
                  context: context,
                  isScrollControlled: true,
                  backgroundColor: Colors.transparent,
                  builder: (context) {
                    final keyboardHeight = MediaQuery.of(context).viewInsets.bottom;
                    final screenHeight = MediaQuery.of(context).size.height;

                    // More precise height calculation for compact content
                    final contentHeight = 280.0; // Estimated content height
                    final baseHeight = contentHeight + 40; // Content + padding
                    final minHeight = screenHeight * 0.25; // 25% minimum
                    final maxHeight = screenHeight * 0.7; // 70% maximum

                    final adjustedHeight = keyboardHeight > 0
                        ? (baseHeight + keyboardHeight * 0.8).clamp(minHeight, maxHeight)
                        : baseHeight.clamp(minHeight, maxHeight);

                    return SizedBox(
                      height: adjustedHeight,
                      child: ShoppingPantryBottomSheetContent(
                        title: 'Create Pantry List',
                        hintText: 'Enter pantry list name',
                        successText: 'Pantry list created successfully',
                        buttonText: 'Create',
                        successButtonText: 'Done',
                        scrollController: ScrollController(),
                        isShoppingList: false,
                        onCreate: (name, context, ref) {
                          // Handle creation logic here
                        },
                      ),
                    );
                  },
                );
              },
            )
          : ImportCreateCardMobileList(
              importText: "Import Pantry",
              title: "Add New Pantry",
              isRecipe: false,
              onImport: () {},
              onCreate: () => ref
                  .read(pageStateProvider.notifier)
                  .showPantryDialog(context, ref),
            );
    }

    // Pantry list item
    final pantryState = ref.watch(pantryNotifierProvider);
    final pantryIndex = index - 1;
    final pantryList = pantryState.data!.pantries![pantryIndex];

    final pantry = Pantry(
      id: pantryList.id!.toInt(),
      title: pantryList.name ?? '',
      imageUrl: pantryList.coverImageUrl ?? '',
      recipeCount: (pantryList.pantryItemCount ?? 0),
      createdDate: pantryList.dateCreated.toString(),
    );

    return currentViewType == ViewType.grid
        ? PantryCard(
            pantry: pantry,
            isHighRes: widget.isHighRes,
            pantryIndex: pantryIndex,
            onTap: () {
              showModalBottomSheet(
                context: context,
                isScrollControlled: true,
                backgroundColor: Colors.transparent,
                builder: (context) {
                  final keyboardHeight = MediaQuery.of(context).viewInsets.bottom;
                  final screenHeight = MediaQuery.of(context).size.height;

                  // More precise height calculation for compact content
                  final contentHeight = 280.0; // Estimated content height
                  final baseHeight = contentHeight + 40; // Content + padding
                  final minHeight = screenHeight * 0.25; // 25% minimum
                  final maxHeight = screenHeight * 0.7; // 70% maximum

                  final adjustedHeight = keyboardHeight > 0
                      ? (baseHeight + keyboardHeight * 0.8).clamp(minHeight, maxHeight)
                      : baseHeight.clamp(minHeight, maxHeight);

                  return SizedBox(
                    height: adjustedHeight,
                    child: ShoppingPantryBottomSheetContent(
                      title: 'Update Pantry List',
                      hintText: 'Enter pantry list name',
                      successText: 'Pantry list updated successfully',
                      buttonText: 'Update',
                      successButtonText: 'Done',
                      scrollController: ScrollController(),
                      isShoppingList: false,
                      callFromUpdate: true,
                      listId: pantryList.id.toString(),
                      listName: pantryList.name,
                      onCreate: (name, context, ref) {
                        // Handle creation logic here
                      },
                    ),
                  );
                },
              );
            },
            selectedTab: widget.selectedTab,
          )
        : PantryListviewItems(
            pantry: pantry,
            isHighRes: widget.isHighRes,
            pantryIndex: pantryIndex,
            onTap: () {
              showModalBottomSheet(
                context: context,
                isScrollControlled: true,
                backgroundColor: Colors.transparent,
                builder: (context) {
                  final keyboardHeight = MediaQuery.of(context).viewInsets.bottom;
                  final screenHeight = MediaQuery.of(context).size.height;

                  // More precise height calculation for compact content
                  final contentHeight = 280.0; // Estimated content height
                  final baseHeight = contentHeight + 40; // Content + padding
                  final minHeight = screenHeight * 0.25; // 25% minimum
                  final maxHeight = screenHeight * 0.7; // 70% maximum

                  final adjustedHeight = keyboardHeight > 0
                      ? (baseHeight + keyboardHeight * 0.8).clamp(minHeight, maxHeight)
                      : baseHeight.clamp(minHeight, maxHeight);

                  return SizedBox(
                    height: adjustedHeight,
                    child: ShoppingPantryBottomSheetContent(
                      title: 'Update Pantry List',
                      hintText: 'Enter pantry list name',
                      successText: 'Pantry list updated successfully',
                      buttonText: 'Update',
                      successButtonText: 'Done',
                      scrollController: ScrollController(),
                      isShoppingList: false,
                      callFromUpdate: true,
                      listId: pantryList.id.toString(),
                      listName: pantryList.name,
                      onCreate: (name, context, ref) {
                        // Handle creation logic here
                      },
                    ),
                  );
                },
              );
            },
            selectedTab: widget.selectedTab,
          );
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final pantryState = ref.watch(pantryNotifierProvider);
    final currentViewType = ref.watch(viewTypeProvider);

    if (pantryState.status == AppStatus.loading &&
        (pantryState.data?.pantries?.isEmpty ?? true)) {
      return CookbookShimmer(
        crossAxisCount: 2,
        itemsPerPage: 10,
      );
    }

    if (pantryState.data?.pantries?.isEmpty ?? true) {
      return NoDataWidget(
        title: "No Pantry Found",
        subtitle: "Try adjusting your search terms or create a new pantry",
        width: 250,
        height: 250,
      );
    }

    return Padding(
      padding: const EdgeInsets.only(top: 16.0),
      child: PaginatedResponsiveGridList<dynamic>(
        items: [
          // Add placeholder for create card
          'create_pantry_card',
          // Add all pantry lists
          ...(pantryState.data?.pantries ?? [])
        ],
        itemBuilder: (item, index) => _buildPantryItem(item, index, currentViewType),
        desiredItemWidth: 164,
        minSpacing: 10,
        pageSize: 20,
        viewType: currentViewType,
        onLoadMore: () {
          if (pantryState.hasMore &&
              pantryState.status != AppStatus.loadingMore) {
            ref.read(pantryNotifierProvider.notifier).fetchPantryLists(
              loadMore: true,
              context: context,
              search: widget.searchQuery,
            );
          }
        },
      ),
    );
  }
}
