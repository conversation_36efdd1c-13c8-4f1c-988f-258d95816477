import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/core/data/models/pantry.dart' as pantry_model;
import 'package:mastercookai/core/data/request_query/pantry_item_request.dart' as pantry_request;
import 'package:mastercookai/core/providers/shopping/pantry_item_notifier.dart';
import 'package:mastercookai/core/providers/shopping/pantry_notifier.dart';
import 'package:mastercookai/presentation/shopping/mobile/helpers/mobile_validation_helpers.dart';
import 'package:mastercookai/presentation/shopping/mobile/widgets/mobile_search_fields.dart';

import '../../../../core/widgets/custom_button.dart';
import '../../../../core/widgets/custom_text.dart';

void showAddPantryProductBottomSheet(BuildContext context, {pantry_model.PantryItem? editItem, int? pantryListId}) {
  showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    backgroundColor: Colors.transparent,
    shape: const RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
    ),
    builder: (context) {
      return AddPantryProductContent(editItem: editItem, pantryListId: pantryListId);
    },
  );
}

class AddPantryProductContent extends ConsumerStatefulWidget {
  final pantry_model.PantryItem? editItem;
  final int? pantryListId;

  const AddPantryProductContent({super.key, this.editItem, this.pantryListId});

  @override
  ConsumerState<AddPantryProductContent> createState() => _AddPantryProductContentState();
}

class _AddPantryProductContentState extends ConsumerState<AddPantryProductContent> {
  late final TextEditingController itemController;
  late final TextEditingController amountController;
  late final TextEditingController unitsController;
  late final TextEditingController purchaseDateController;
  late final TextEditingController useByDateController;
  late final bool isEditing;

  @override
  void initState() {
    super.initState();
    final editItem = widget.editItem;
    isEditing = editItem != null;

    itemController = TextEditingController(text: editItem?.item ?? '');
    amountController = TextEditingController(text: editItem?.amount.toString() ?? '');
    unitsController = TextEditingController(text: editItem?.unit ?? '');
    purchaseDateController = TextEditingController(
        text: editItem?.purchasedDate != null
            ? DateFormat('MM/dd/yyyy').format(editItem!.purchasedDate)
            : '');
    useByDateController = TextEditingController(
        text: editItem?.useByDate != null
            ? DateFormat('MM/dd/yyyy').format(editItem!.useByDate)
            : '');
  }

  @override
  void dispose() {
    itemController.dispose();
    amountController.dispose();
    unitsController.dispose();
    purchaseDateController.dispose();
    useByDateController.dispose();
    super.dispose();
  }

  Future<void> selectDate(TextEditingController controller) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime(2101),
    );
    if (picked != null) {
      controller.text = DateFormat('MM/dd/yyyy').format(picked);
    }
  }

  Future<void> saveProduct() async {
    final item = itemController.text.trim();
    final amountText = amountController.text.trim();
    final unit = unitsController.text.trim();
    final purchaseDateText = purchaseDateController.text.trim();
    final useByDateText = useByDateController.text.trim();

    final validationErrors = MobileValidationHelpers.validatePantryItem(
      item: item,
      amount: amountText,
      unit: unit,
      purchaseDate: purchaseDateText,
      useByDate: useByDateText,
    );

    if (validationErrors.isNotEmpty) {
      MobileValidationHelpers.showValidationError(context, validationErrors.first);
      return;
    }

    final amount = MobileValidationHelpers.parseAmount(amountText);
    final purchasedDate = MobileValidationHelpers.parseDate(purchaseDateText);
    final useByDate = MobileValidationHelpers.parseDate(useByDateText);

    if (isEditing) {
      final updatedPantryItem = pantry_request.PantryItem(
        id: widget.editItem!.id,
        item: item,
        amount: amount,
        unit: unit.isNotEmpty ? unit : widget.editItem!.unit,
        purchasedDate: purchasedDate ?? widget.editItem!.purchasedDate,
        useByDate: useByDate ?? widget.editItem!.useByDate,
      );

      final request = pantry_request.PantryItemsRequest(
        pantryItems: [updatedPantryItem],
      );

      final targetPantryListId = widget.pantryListId ?? _getPantryListId(ref);
      if (targetPantryListId == null) {
        MobileValidationHelpers.showValidationError(context, 'Invalid pantry list');
        return;
      }

      final (success, errorMessage) = await ref
          .read(pantryItemNotifierProvider.notifier)
          .addPantryItems(
            pantryListId: targetPantryListId,
            request: request,
          );

      if (success) {
        MobileValidationHelpers.showSuccessMessage(context, 'Item updated successfully');
        if (mounted) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted) {
              Navigator.of(context).pop();
            }
          });
        }
        await ref.read(pantryItemNotifierProvider.notifier).fetchPantryItems(
              id: targetPantryListId,
              search: null,
            );
      } else {
        MobileValidationHelpers.showValidationError(context, errorMessage ?? 'Failed to update item');
      }
    } else {
      final targetPantryListId = widget.pantryListId ?? _getPantryListId(ref);
      if (targetPantryListId == null) {
        MobileValidationHelpers.showValidationError(context, 'No pantry list available');
        return;
      }

      final pantryItem = pantry_request.PantryItem(
        item: item,
        amount: amount,
        unit: unit,
        purchasedDate: purchasedDate,
        useByDate: useByDate,
      );

      final request = pantry_request.PantryItemsRequest(
        pantryItems: [pantryItem],
      );

      final (success, errorMessage) = await ref
          .read(pantryItemNotifierProvider.notifier)
          .addPantryItems(
            pantryListId: targetPantryListId,
            request: request,
          );

      if (success) {
        MobileValidationHelpers.showSuccessMessage(context, 'Item added successfully');
        if (mounted) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted) {
              Navigator.of(context).pop();
            }
          });
        }
        await ref.read(pantryItemNotifierProvider.notifier).fetchPantryItems(
              id: targetPantryListId,
              search: null,
            );
      } else {
        MobileValidationHelpers.showValidationError(context, errorMessage ?? 'Failed to add item');
      }
    }
  }

  void closeSheet() {
    Navigator.of(context).pop();
  }

  int? _getPantryListId(WidgetRef ref) {
    final pantryState = ref.read(pantryNotifierProvider);
    final pantryLists = pantryState.data?.pantries ?? [];
    if (pantryLists.isEmpty) return null;
    return pantryLists.first.id;
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
      child: SingleChildScrollView(
        child: _buildPantryForm(
          context,
          ref,
          itemController,
          amountController,
          unitsController,
          purchaseDateController,
          useByDateController,
          saveProduct,
          closeSheet,
          isEditing,
          selectDate,
        ),
      ),
    );
  }
}

Widget _buildPantryForm(
  BuildContext context,
  WidgetRef ref,
  TextEditingController itemController,
  TextEditingController amountController,
  TextEditingController unitsController,
  TextEditingController purchaseDateController,
  TextEditingController useByDateController,
  Future<void> Function() saveProduct,
  VoidCallback closeDialog,
  bool isEditing,
  Future<void> Function(TextEditingController) selectDate,
) {
  return Container(
    decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            spreadRadius: 0,
            blurRadius: 20,
            offset: const Offset(0, -5),
          ),
        ]),
    child: Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        const SizedBox(height: 10),
        Container(
          width: 40,
          height: 4,
          decoration: BoxDecoration(
            color: Colors.grey[300],
            borderRadius: BorderRadius.circular(2),
          ),
        ),
        const SizedBox(height: 10),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const SizedBox(height: 16),
              CustomText(
                text: isEditing ? "Edit Pantry Item" : "Add Pantry Items",
                color: AppColors.primaryGreyColor,
                size: 24,
                weight: FontWeight.w700,
              ),
              const SizedBox(height: 25),
              MobileItemSearchField(
                title: 'Items',
                placeholder: 'Search for items...',
                controller: itemController,
                isRequired: true,
              ),
              const SizedBox(height: 16),
              MobileTextField(
                title: 'Amount',
                placeholder: 'Enter amount',
                controller: amountController,
                keyboardType: const TextInputType.numberWithOptions(decimal: true),
                maxLength: 14,
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                ],
              ),
              const SizedBox(height: 16),
              MobileUnitSearchField(
                title: 'Units',
                placeholder: 'Search for units...',
                controller: unitsController,
              ),
              const SizedBox(height: 16),
              MobileDateField(
                title: 'Purchased Date',
                placeholder: 'Select Date',
                controller: purchaseDateController,
                onTap: () => selectDate(purchaseDateController),
                isRequired: true,
              ),
              const SizedBox(height: 16),
              MobileDateField(
                title: 'Use By Date',
                placeholder: 'Select Date',
                controller: useByDateController,
                onTap: () => selectDate(useByDateController),
                isRequired: true,
              ),
            ],
          ),
        ),
        const SizedBox(height: 20),
        CustomButton(
          text: isEditing ? 'Update' : 'Save',
          height: 40,
          width: 200,
          weight: FontWeight.w600,
          fontSize: 16,
          onPressed: saveProduct,
          borderRadius: 10,
          color: Colors.red,
        ),
        const SizedBox(height: 20),
      ],
    ),
  );
}
