import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/core/data/models/shopping.dart';
import 'package:mastercookai/core/data/request_query/shopping_item_request.dart' as shopping_request;
import 'package:mastercookai/core/providers/shopping/shopping_item_notifier.dart';
import 'package:mastercookai/core/providers/shopping/shopping_notifier.dart';
import 'package:mastercookai/presentation/shopping/mobile/helpers/mobile_validation_helpers.dart';
import 'package:mastercookai/presentation/shopping/mobile/widgets/mobile_search_fields.dart';

import '../../../../core/widgets/custom_button.dart';
import '../../../../core/widgets/custom_text.dart';

void showAddProductBottomSheet(BuildContext context, {ShoppingItem? editItem, int? shoppingListId}) {
  showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    backgroundColor: Colors.transparent, // Make it transparent to see the custom decoration
    shape: const RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
    ),
    builder: (context) {
      return AddProductContent(editItem: editItem, shoppingListId: shoppingListId);
    },
  );
}

class AddProductContent extends ConsumerStatefulWidget {
  final ShoppingItem? editItem;
  final int? shoppingListId;

  const AddProductContent({super.key, this.editItem, this.shoppingListId});

  @override
  ConsumerState<AddProductContent> createState() => _AddProductContentState();
}

class _AddProductContentState extends ConsumerState<AddProductContent> {
  late final TextEditingController itemController;
  late final TextEditingController amountController;
  late final TextEditingController unitsController;
  late final TextEditingController storeLocationController;
  late final TextEditingController recipeController;
  late final TextEditingController costController;
  late final bool isEditing;

  @override
  void initState() {
    super.initState();
    final editItem = widget.editItem;
    isEditing = editItem != null;

    itemController = TextEditingController(text: editItem?.item ?? '');
    amountController = TextEditingController(text: editItem?.amount.toString() ?? '');
    unitsController = TextEditingController(text: editItem?.unit ?? '');
    storeLocationController = TextEditingController(text: editItem?.storeLocation ?? '');
    recipeController = TextEditingController(text: editItem?.recipe ?? '');
    costController = TextEditingController(text: editItem?.cost.toString() ?? '');
  }

  @override
  void dispose() {
    itemController.dispose();
    amountController.dispose();
    unitsController.dispose();
    storeLocationController.dispose();
    recipeController.dispose();
    costController.dispose();
    super.dispose();
  }

  Future<void> saveProduct() async {
    final item = itemController.text.trim();
    final amountText = amountController.text.trim();
    final unit = unitsController.text.trim();
    final storeLocation = storeLocationController.text.trim();
    final recipe = recipeController.text.trim();
    final costText = costController.text.trim();

    final validationErrors = MobileValidationHelpers.validateShoppingItem(
      item: item,
      amount: amountText,
      unit: unit,
      storeLocation: storeLocation,
      recipe: recipe,
      cost: costText,
    );

    if (validationErrors.isNotEmpty) {
      MobileValidationHelpers.showValidationError(context, validationErrors.first);
      return;
    }

    final amount = MobileValidationHelpers.parseAmount(amountText);
    final cost = MobileValidationHelpers.parseCost(costText);

    if (isEditing) {
      final updatedShoppingItem = shopping_request.ShoppingItem(
        id: widget.editItem!.id,
        item: item,
        amount: amount,
        unit: unit.isNotEmpty ? unit : widget.editItem!.unit,
        storeLocation: storeLocation.isNotEmpty ? storeLocation : widget.editItem!.storeLocation,
        recipe: recipe.isNotEmpty ? recipe : widget.editItem!.recipe,
        cost: cost,
      );

      final request = shopping_request.ShoppingItemRequest(
        shoppingItems: [updatedShoppingItem],
      );

      final targetShoppingListId = widget.shoppingListId ?? _getShoppingListId(ref);
      if (targetShoppingListId == null) {
        MobileValidationHelpers.showValidationError(context, 'Invalid shopping list');
        return;
      }

      final (success, errorMessage) = await ref
          .read(shoppingItemNotifierProvider.notifier)
          .addShoppingItems(
        shoppingListId: targetShoppingListId,
        request: request,
      );

      if (success) {
        MobileValidationHelpers.showSuccessMessage(context, 'Item updated successfully');
        if (mounted) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            // if (mounted) {
            //   Navigator.of(context).pop();
            // }
          });
        }
        await ref.read(shoppingItemNotifierProvider.notifier).fetchShoppingListsItems(
          id: targetShoppingListId,
          search: null,
        );
      } else {
        MobileValidationHelpers.showValidationError(context, errorMessage ?? 'Failed to update item');
      }
    } else {
      final targetShoppingListId = widget.shoppingListId ?? _getShoppingListId(ref);
      if (targetShoppingListId == null) {
        MobileValidationHelpers.showValidationError(context, 'No shopping list available');
        return;
      }

      final shoppingItem = shopping_request.ShoppingItem(
        item: item,
        amount: amount,
        unit: unit,
        storeLocation: storeLocation,
        recipe: recipe,
        cost: cost,
      );

      final request = shopping_request.ShoppingItemRequest(
        shoppingItems: [shoppingItem],
      );

      final (success, errorMessage) = await ref
          .read(shoppingItemNotifierProvider.notifier)
          .addShoppingItems(
        shoppingListId: targetShoppingListId,
        request: request,
      );

      if (success) {
        MobileValidationHelpers.showSuccessMessage(context, 'Item added successfully');
        if (mounted) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            // if (mounted) {
            //   Navigator.of(context).pop();
            // }
          });
        }
        await ref.read(shoppingItemNotifierProvider.notifier).fetchShoppingListsItems(
          id: targetShoppingListId,
          search: null,
        );
      } else {
        MobileValidationHelpers.showValidationError(context, errorMessage ?? 'Failed to add item');
      }
    }
  }

  void closeSheet() {
    Navigator.of(context).pop();
  }

  int? _getShoppingListId(WidgetRef ref) {
    final shoppingState = ref.read(shoppingNotifierProvider);
    final shoppingLists = shoppingState.data?.shoppingLists ?? [];
    if (shoppingLists.isEmpty) return null;
    return shoppingLists.first.id?.toInt();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
      child: SingleChildScrollView(
        child: _buildProductForm(
          context,
          ref,
          itemController,
          amountController,
          unitsController,
          storeLocationController,
          recipeController,
          costController,
          saveProduct,
          closeSheet,
          isEditing,
        ),
      ),
    );
  }
}

Widget _buildProductForm(
    BuildContext context,
    WidgetRef ref,
    TextEditingController itemController,
    TextEditingController amountController,
    TextEditingController unitsController,
    TextEditingController storeLocationController,
    TextEditingController recipeController,
    TextEditingController costController,
    Future<void> Function() saveProduct,
    VoidCallback closeDialog,
    bool isEditing,
    ) {
  return Container(
    decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            spreadRadius: 0,
            blurRadius: 20,
            offset: const Offset(0, -5),
          ),
        ]),
    child: Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        const SizedBox(height: 10),
        Container(
          width: 40,
          height: 4,
          decoration: BoxDecoration(
            color: Colors.grey[300],
            borderRadius: BorderRadius.circular(2),
          ),
        ),
        const SizedBox(height: 10),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const SizedBox(height: 16),
              CustomText(
                text: isEditing ? "Edit Shopping Item" : "Add Shopping Items",
                color: AppColors.primaryGreyColor,
                size: 24,
                weight: FontWeight.w700,
              ),
              const SizedBox(height: 25),
              MobileItemSearchField(
                title: 'Items',
                placeholder: 'Search for items...',
                controller: itemController,
                isRequired: true,
              ),
              const SizedBox(height: 16),
              MobileTextField(
                title: 'Amount',
                placeholder: 'Enter amount',
                controller: amountController,
                keyboardType: const TextInputType.numberWithOptions(decimal: true),
                maxLength: 14,
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                ],
              ),
              const SizedBox(height: 16),
              MobileUnitSearchField(
                title: 'Units',
                placeholder: 'Search for units...',
                controller: unitsController,
              ),
              const SizedBox(height: 16),
              MobileTextField(
                title: 'Store Location',
                placeholder: 'Enter store location',
                controller: storeLocationController,
              ),
              const SizedBox(height: 16),
              MobileTextField(
                title: 'Recipe',
                placeholder: 'Enter reciepe',
                controller: recipeController,
              ),
              const SizedBox(height: 16),
              MobileTextField(
                title: 'Cost',
                placeholder: 'Enter cost',
                controller: costController,
                keyboardType: const TextInputType.numberWithOptions(decimal: true),
                maxLength: 10,
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                ],
              ),
            ],
          ),
        ),
        const SizedBox(height: 20),
        CustomButton(
          text: isEditing ? 'Update' : 'Save',
          height: 40,
          width: 200,
          weight: FontWeight.w600,
          fontSize: 16,
          onPressed: saveProduct,
          borderRadius: 10,
          color: Colors.red,
        ),
        const SizedBox(height: 20),
      ],
    ),
  );
}