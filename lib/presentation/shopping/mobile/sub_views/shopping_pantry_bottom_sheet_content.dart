import 'dart:io';

import 'package:flutter_svg/svg.dart';
import 'package:mastercookai/app/imports/core_imports.dart';

import '../../../../app/imports/packages_imports.dart';
import '../../../../core/network/app_status.dart';
import '../../../../core/providers/shopping/pantry_notifier.dart';
import '../../../../core/providers/shopping/shopping_notifier.dart';
import '../../../../core/utils/Utils.dart';
import '../../../../core/utils/device_utils.dart';
import '../../../../core/widgets/custom_button.dart';
import '../../../../core/widgets/custom_input_field.dart';
import '../../../../core/widgets/custom_text.dart';

class ShoppingPantryBottomSheetContent extends HookConsumerWidget {
  final String title;
  final String hintText;
  final String successText;
  final String buttonText;
  final String successButtonText;
  final bool callFromUpdate;
  final bool isScrollable;
  final VoidCallback? onSuccess;
  final Function(String, BuildContext, WidgetRef)? onCreate;
  final String? listId;
  final String? listName;
  final ScrollController scrollController;
  final bool isShoppingList;

  const ShoppingPantryBottomSheetContent({
    super.key,
    required this.title,
    required this.hintText,
    required this.successText,
    required this.buttonText,
    required this.successButtonText,
    this.callFromUpdate = false,
    this.isScrollable = false,
    this.onSuccess,
    this.onCreate,
    this.listId,
    this.listName,
    required this.scrollController,
    required this.isShoppingList,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final notifier = isShoppingList
        ? ref.read(shoppingNotifierProvider.notifier)
        : ref.read(pantryNotifierProvider.notifier);
    final appState = isShoppingList
        ? ref.watch(shoppingNotifierProvider)
        : ref.watch(pantryNotifierProvider);
    final nameController = useTextEditingController(text: listName ?? '');
    final formKey = useMemoized(() => GlobalKey<FormState>());

    ref.listen(
        isShoppingList
            ? shoppingNotifierProvider
            : pantryNotifierProvider, (previous, next) {
      if (next.status == AppStatus.error && next.errorMessage != null) {
        Utils().showSnackBar(context, next.errorMessage!);
      }
    });

    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      decoration: BoxDecoration(
        color: context.theme.scaffoldBackgroundColor,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(26)),
      ),
      padding: EdgeInsets.only(
        left: 25,
        right: 25,
        top: 10, // Reduced top padding to accommodate drag handle
        bottom: MediaQuery.of(context).viewInsets.bottom,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
// Drag handle
          Container(
            margin: const EdgeInsets.only(top: 10.0),
            width: 40.0,
            height: 5.0,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2.5),
            ),
          ),
          Flexible(
            child: SingleChildScrollView(
              controller: scrollController,
              child: appState.status ==
                      (callFromUpdate
                          ? AppStatus.updateSuccess
                          : AppStatus.createSuccess)
                  ? _buildSuccessContent(context, ref)
                  : buildFormContent(
                      context, ref, nameController, formKey, notifier),
            ),
          ),
        ],
      ),
    );
  }

  Widget buildFormContent(
      BuildContext context,
      WidgetRef ref,
      TextEditingController controller,
      GlobalKey<FormState> formKey,
      dynamic notifier) {
    final appState = isShoppingList
        ? ref.watch(shoppingNotifierProvider)
        : ref.watch(pantryNotifierProvider);

    return Form(
      key: formKey,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
            //  _closeButton(context, ref),
            const SizedBox(height: 16),
            CustomText(
              text: title,
              color: AppColors.blackTextColor,
              size: 24,
              weight: FontWeight.w700,
            ),
            const SizedBox(height: 30),
            CustomInputField(
              hintText: hintText,
              controller: controller,
              keyboardType: TextInputType.text,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Please enter list name';
                }
                if (value.trim().length < 3) {
                  return 'List name must be at least 3 characters';
                }
                if (value.trim().length > 100) {
                  return 'List name must not exceed 100 characters';
                }
                if (RegExp(r'\s{2,}').hasMatch(value)) {
                  return 'List name should not contain multiple spaces in a row';
                }
                if (value != value.trim()) {
                  return 'List name should not start or end with a space';
                }
                return null;
              },
            ),
            const SizedBox(height: 30),
            CustomButton(
              width: 200,
              fontSize: 14,
              weight: FontWeight.w600,
              borderRadius: 10,
              text: buttonText,
              isLoading: appState.status ==
                  (callFromUpdate ? AppStatus.updating : AppStatus.creating),
              onPressed: () async {
                if (formKey.currentState!.validate()) {
                  try {
                    if (isShoppingList) {
                      if (callFromUpdate) {
                        await (notifier as ShoppingNotifier).updateShopping(
                          context,
                          id: listId!,
                          name: controller.text.trim(),
                          filePath: File(''),
                          callFromUpdate: false,
                        );
                      } else {
                        await (notifier as ShoppingNotifier)
                            .createShoppingList(context, controller.text.trim());
                      }
                    } else {
                      if (callFromUpdate) {
                        await (notifier as PantryNotifier).updatePantry(
                          context,
                          id: listId!,
                          name: controller.text.trim(),
                          filePath: File(''),
                          callFromUpdate: false,
                        );
                      } else {
                        await (notifier as PantryNotifier)
                            .createPantryList(context, controller.text.trim());
                      }
                    }
                    if (onCreate != null) {
                      await onCreate!(controller.text.trim(), context, ref);
                    }
                  } catch (e) {
                    Utils().showSnackBar(context, 'Error: $e');
                  }
                }
              },
            ),
            const SizedBox(height: 20),
          ],
        ),
    );
  }

  Widget _buildSuccessContent(BuildContext context, WidgetRef ref) {
    return SizedBox(
      width: MediaQuery.of(context).size.width,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const SizedBox(height: 16),
          // _closeButton(context, ref),
          SvgPicture.asset(AssetsManager.success, height: 80, width: 80),
          const SizedBox(height: 30),
          CustomText(
            text: successText,
            color: AppColors.primaryGreyColor,
            size: 14,
            weight: FontWeight.w400,
            align: TextAlign.center,
          ),
          const SizedBox(height: 30),
          CustomButton(
            width: 250,
            fontSize: 14,
            borderRadius: 10,
            weight: FontWeight.w600,
            isLoading: ref
                    .watch(isShoppingList
                        ? shoppingNotifierProvider
                        : pantryNotifierProvider)
                    .status ==
                AppStatus.loading,
            text: successButtonText,
            onPressed: () {
              Navigator.of(context).pop();
              if (isShoppingList) {
                ref
                    .read(shoppingNotifierProvider.notifier)
                    .fetchShoppingLists(context: context);
              } else {
                ref
                    .read(pantryNotifierProvider.notifier)
                    .fetchPantryLists(context: context);
              }
              if (onSuccess != null) {
                onSuccess!();
              }
            },
          ),
        ],
      ),
    );
  }

  Widget _closeButton(BuildContext context, WidgetRef ref) {
    return Align(
      alignment: Alignment.topRight,
      child: IconButton(
        icon: const Icon(IconsaxPlusBold.close_circle, color: Colors.red),
        onPressed: () {
          if (isShoppingList) {
            ref.read(shoppingNotifierProvider.notifier).resetToIdle();
          } else {
            ref.read(pantryNotifierProvider.notifier).resetToIdle();
          }
          Navigator.of(context).pop();
        },
      ),
    );
  }
}
