import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/core/network/app_status.dart';
 import 'package:mastercookai/core/providers/shopping/page_state_provider.dart';
import 'package:mastercookai/presentation/shopping/sub_view/pantry_card.dart';
import 'package:mastercookai/presentation/shopping/sub_view/update_pantry_dialog.dart';
import '../../../app/imports/packages_imports.dart';
import '../../../core/data/models/pantry.dart';
import '../../../core/providers/shopping/pantry_notifier.dart';
import '../../../core/utils/device_utils.dart';
import '../../../core/utils/screen_sizer.dart';
import '../../../core/widgets/common_paginated_grid_view.dart';
import '../../../core/widgets/no_data_widget.dart';
import '../../cookbook/widgets/import_create_card.dart';
import '../../shimer/recipe_shimmer.dart';
import 'shopping_dialog.dart';

class PantryView extends ConsumerStatefulWidget {
  final Size screenSize;
  final bool isHighRes;
  final String? searchQuery;
  final  String selectedTab;


  const PantryView({
    Key? key,
    required this.screenSize,
    required this.isHighRes,
    this.searchQuery,
    required this.selectedTab,
  }) : super(key: key);

  @override
  ConsumerState<PantryView> createState() => _PantryViewState();
}

class _PantryViewState extends ConsumerState<PantryView>
    with AutomaticKeepAliveClientMixin {
  late final PageController _pageController;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    final currentPage = ref.read(pageStateProvider).pantryCurrentPage;
    _pageController = PageController(initialPage: currentPage);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_pageController.hasClients) {
        _pageController.jumpToPage(currentPage);
      }
    });
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final pantryState = ref.watch(pantryNotifierProvider);
    final screenSize = widget.screenSize;
    final isHighRes = widget.isHighRes;
    final pageState = ref.watch(pageStateProvider);
    final currentPage = pageState.pantryCurrentPage;

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_pageController.hasClients &&
          _pageController.page?.round() != currentPage) {
        _pageController.jumpToPage(currentPage);
      }
    });

    final crossAxisCount = DeviceUtils().isTabletOrIpad(context)
        ? 3
        : isHighRes
            ? 5
            : screenSize.width > 600
                ? 5
                : 2;

    if (pantryState.status == AppStatus.loading ||
        pantryState.status == AppStatus.error ||
        pantryState.data == null ||
        pantryState.data!.pantries == null ||
        pantryState.data!.pantries!.isEmpty) {
      return RecipeShimmer(
        crossAxisCount: crossAxisCount,
        itemsPerPage: 8,
        childAspectRatio: 1.0,
      );
    } else if (pantryState.data?.pantries?.isNotEmpty ?? false) {
      final itemsPerPage = crossAxisCount * 2;
      final totalItems = (pantryState.data?.pantries?.length ?? 0) + 1;
      final totalPages = (totalItems / itemsPerPage).ceil();

      return NotificationListener<ScrollNotification>(
        onNotification: (scrollNotification) {
          if (scrollNotification is ScrollEndNotification) {
            final metrics = scrollNotification.metrics;
            if (metrics.pixels >= metrics.maxScrollExtent - 200 &&
                pantryState.hasMore &&
                pantryState.status != AppStatus.loadingMore) {
              ref.read(pantryNotifierProvider.notifier).fetchPantryLists(
                    loadMore: true,
                    context: context,
                    search: widget.searchQuery,
                  );
            }
          }
          return false;
        },
        child: Stack(
          children: [
            Column(
              children: [
                Expanded(
                  child: CommonPaginatedGridView(
                    pageController: _pageController,
                    state: pantryState,
                    totalPages: totalPages,
                    totalItems: totalItems,
                    itemsPerPage: itemsPerPage,
                    crossAxisCount: crossAxisCount,
                    currentSearchQuery: widget.searchQuery ?? '',
                    isHighRes: isHighRes,

                    ref: ref,
                    context: context,
                    onPageChanged: (int page) {
                      ref
                          .read(pageStateProvider.notifier)
                          .updatePantryPage(page);
                      if (page == totalPages - 1 && pantryState.hasMore) {
                        ref
                            .read(pantryNotifierProvider.notifier)
                            .fetchPantryLists(
                              loadMore: true,
                              context: context,
                              search: widget.searchQuery,
                            );
                      }
                    },
                    itemBuilder:
                        (BuildContext context, int index, int itemIndex) {
                       if (itemIndex == totalItems - 1 &&
                          pantryState.hasMore &&
                          pantryState.status == AppStatus.loadingMore) {
                        return Center(
                          child: LoadingAnimationWidget.fallingDot(
                            color: Colors.white,
                            size: 50.0,
                          ),
                        );
                      }

                      if (itemIndex == 0) {
                        return ImportCreateCard(
                          importText: "Import Pantry",
                          title: "Add New Pantry",
                          isRecipe: false,
                          onImport: (){

                          },
                          onCreate: () => ref.read(pageStateProvider.notifier).showPantryDialog(context, ref),
                        );
                      }

                      final pantryIndex = itemIndex - 1;
                      if (pantryIndex <
                          (pantryState.data?.pantries?.length ?? 0)) {
                        final pantryList = pantryState.data?.pantries?[pantryIndex];
                        final pantry = Pantry(
                          id: pantryList?.id??0,
                          title: pantryList?.name ?? '',
                          imageUrl: pantryList?.coverImageUrl ?? '',
                          recipeCount: pantryList?.pantryItemCount ?? 0,
                          createdDate: pantryList?.dateCreated.toString()??'',
                        );
                        return PantryCard(
                          pantry: pantry,
                          pantryIndex:pantryIndex,
                          isHighRes: isHighRes,
                          onTap: () {
                            ref
                                .read(pantryNotifierProvider.notifier)
                                .resetToIdle();
                            showUpdatePantryDialog(
                              context,
                              ref,
                              pantryId: pantryList?.id.toString(),
                              pantryName: pantryList?.name,
                            );
                          },
                          selectedTab: widget.selectedTab,
                        );
                      }

                      return const SizedBox.shrink();
                    },
                  ),
                ),
              ],
            ),
            if (totalPages > 1)
              Positioned(
                left: 0,
                right: 0,
                bottom: screenSize.height * 0.15,
                child: Center(
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: List<Widget>.generate(
                      pantryState.hasMore ? totalPages + 1 : totalPages,
                      (int index) {
                        if (index >= totalPages) {
                          return const SizedBox.shrink();
                        }
                        return Container(
                          width: screenSize.width * 0.008,
                          height: screenSize.width * 0.008,
                          margin: EdgeInsets.symmetric(
                            horizontal: screenSize.width * 0.004,
                          ),
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: currentPage == index
                                ? context.theme.scaffoldBackgroundColor
                                : context.theme.scaffoldBackgroundColor
                                    .withValues(alpha: .2),
                          ),
                        );
                      },
                    ),
                  ),
                ),
              ),
          ],
        ),
      );
    } else if (pantryState.status != AppStatus.idle &&
        (pantryState.data?.pantries?.isEmpty ?? true)) {
      if (widget.searchQuery != null && widget.searchQuery!.isNotEmpty) {
        return const NoDataWidget(
          title: "No Pantry Lists Found",
          subtitle:
              "Try adjusting your search terms or create a new pantry list",
          width: 250,
          height: 250,
        );
      } else {
        final crossAxisCount = DeviceUtils().isTabletOrIpad(context)
            ? 3
            : isHighRes
                ? 5
                : screenSize.width > 600
                    ? 5
                    : 2;
        return LayoutBuilder(
          builder: (context, constraints) {
            final size = MediaQuery.of(context).size;
            final aspectRatio =
                ScreenSizer().calculateShoppingSize(size.width, size.height);
            return GridView.builder(
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: crossAxisCount,
                mainAxisSpacing: screenSize.height * 0.01,
                crossAxisSpacing: screenSize.width * 0.01,
                childAspectRatio: aspectRatio,
              ),
              itemCount: 1,
              itemBuilder: (context, index) {
                return ImportCreateCard(
                  importText: "Import Pantry",
                  title: "Add New Pantry",
                  isRecipe: false,
                  onImport: ()  {

                  },
                  onCreate: () => ref.read(pageStateProvider.notifier).showPantryDialog(context, ref),
                );
              },
            );
          },
        );
      }
    } else {
      return const SizedBox.shrink();
    }
  }




}
