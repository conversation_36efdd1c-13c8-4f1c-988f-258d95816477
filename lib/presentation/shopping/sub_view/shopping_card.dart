import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/core/providers/shopping/shopping_notifier.dart';
import 'package:mastercookai/core/network/app_status.dart';
import 'package:mastercookai/core/utils/device_utils.dart';
import 'package:mastercookai/core/utils/screen_sizer.dart';
import 'package:mastercookai/core/widgets/common_image.dart';
import 'package:timeago/timeago.dart' as timeago;

import '../../../../../core/widgets/custom_button.dart';
import '../../../../core/data/models/shopping.dart'; // Removed unused import
import '../../../core/utils/Utils.dart';
import '../../../core/widgets/camera_upload_image.dart';
import '../../../core/widgets/custom_hover_menu.dart';

class ShoppingCard extends ConsumerWidget {
  final Shopping shopping;
  final bool isHighRes;
  final VoidCallback onTap;
  final String selectedTab;
  final int shoppingIndex;

  ShoppingCard({
    super.key,
    required this.shopping,
    required this.isHighRes,
    required this.onTap,
    required this.selectedTab,
    required this.shoppingIndex,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final screenSize = MediaQuery.of(context).size;
    final shoppingListNotifier = ref.read(shoppingNotifierProvider.notifier);
    final shoppingNotifier = ref.read(shoppingNotifierProvider.notifier);
    final shoppingListState = ref.watch(shoppingNotifierProvider);

    // Show loading indicator when deleting
    final isDeleting = shoppingListState.status == AppStatus.loading;

    return Card(
      color: context.theme.cardColor,
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10),
      ),
      child: Padding(
        padding: EdgeInsets.only(
            left: screenSize.width * 0.005,
              right: screenSize.width * 0.005,
            top: screenSize.width * 0.005),
        child: Stack(
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Stack(
                  children: [
                    ClipRRect(
                        borderRadius: const BorderRadius.vertical(
                          top: Radius.circular(15),
                          bottom: Radius.circular(15),
                        ),
                        child: CommonImage(
                            imageSource: shopping.imageUrl,
                            width: screenSize.width,
                            fit: BoxFit.cover,
                            placeholder: AssetsManager.shoppingDummy,
                            height:
                                ScreenSizer().calculateImageHeight(context))),
                    cameraUploadImage(onTap: () {
                      shoppingNotifier.pickImage(
                          context, ref, shopping.id.toString(), shopping.title);
                    })
                  ],
                ),
                SizedBox(height: 16.h),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Flexible(
                          child: Text(
                            shopping.title,
                            style: context.theme.textTheme.bodyMedium!.copyWith(
                              color: AppColors.primaryGreyColor,
                              fontSize: DeviceUtils().isTabletOrIpad(context)
                                  ? 14
                                  : responsiveFont(28).sp,
                              fontWeight: FontWeight.w400,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        CustomHoverMenu(
                          items: ['Edit', 'Delete'],
                          itemIcons: [Icons.edit, Icons.delete],
                          // Icons for each item
                          onItemSelected: (value) async {
                            if (value == 'Edit') {
                              onTap();
                            } else if (value == 'Delete') {
                              if (!isDeleting) {
                                // Show confirmation dialog
                                final bool? confirmed =
                                    await Utils().showCommonConfirmDialog(
                                  context: context,
                                  title: 'Delete Shopping List',
                                  subtitle:
                                      'Are you sure you want to delete "${shopping.title}" shopping list?',
                                  confirmText: 'Delete',
                                  cancelText: 'Cancel',
                                );

                                if (confirmed != true || !context.mounted) {
                                  return; // User cancelled deletion or context is no longer valid
                                }

                                final result = await shoppingListNotifier
                                    .deleteShoppingList(
                                        context, shopping.id.toString());

                                if (!context.mounted) {
                                  return; // Context is no longer valid after async operation
                                }

                                if (result.$1 == AppStatus.success) {
                                  // Refresh the shopping list screen
                                  shoppingNotifier.fetchShoppingLists(
                                      context: context);
                                }
                              }
                            }
                          },
                          menuWidth: 140.0,
                          // Matches previous menu width
                          menuTitle: 'Show Menu',
                          // Tooltip on hover
                          triggerIcon:
                              Icons.more_vert_outlined, // Custom trigger icon
                        ),
                      ],
                    ),
                    SizedBox(
                        height:
                            DeviceUtils().isTabletOrIpad(context) ? 8 : 16.h),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text('${shopping.recipeCount} products',
                            style: context.theme.textTheme.labelSmall!.copyWith(
                              color: AppColors.textGreyColor,
                              fontSize: DeviceUtils().isTabletOrIpad(context)
                                  ? 12
                                  : 22.sp,
                              fontWeight: FontWeight.w400,
                            )),
                        Text(
                            'Created : ${timeago.format(DateTime.parse(shopping.createdDate), locale: 'en_short')}',
                            style: context.theme.textTheme.labelSmall!.copyWith(
                              color: AppColors.textGreyColor,
                              fontSize: DeviceUtils().isTabletOrIpad(context)
                                  ? 10
                                  : 20.sp,
                              fontWeight: FontWeight.w400,
                            )),
                      ],
                    ),
                    SizedBox(
                        height: DeviceUtils().isTabletOrIpad(context) ? 8 : 20),
                    CustomButton(
                      text: 'Open Shopping List',
                      fontSize:
                          16,
                      onPressed: () {
                        if (!isDeleting) {
                          context.go(
                              '/shopping/shopping_detail_views?tab=$selectedTab&index=$shoppingIndex');

                        }
                      },
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
