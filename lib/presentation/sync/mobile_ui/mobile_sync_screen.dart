import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/core/widgets/custom_text.dart';
import 'package:mastercookai/presentation/shopping/mobile/sub_views/add_product_bottom_sheet.dart';
import 'package:mastercookai/presentation/shopping/mobile/sub_views/mobile_shopping_list_item.dart';
import 'package:mastercookai/presentation/sync/mobile_ui/sync_info_bottom_sheet.dart';
import '../../../app/assets_manager.dart';
import '../../../app/imports/packages_imports.dart';
import '../../../app/theme/colors.dart';
import '../../../core/providers/sync_notifier.dart';
import '../../../core/utils/Utils.dart';
import '../../../core/widgets/custom_button.dart';

class MobileSyncScreen extends ConsumerWidget {
  const MobileSyncScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    bool isViewScreen =
        true; // Set to true for the view screen, false for other screens
    // Ensure sync is initialized
    final syncNotifier = ref.watch(syncNotifierProvider.notifier);
    final syncState = ref.watch(syncNotifierProvider);
    final categories = syncNotifier.categories;
    final progress = syncNotifier.progress;
    final items = syncNotifier.items;

    // Calculate category counts
    final cookbookCount =
        items.where((item) => item['category'] == 'Cookbooks').length;
    final shoppingCount =
        items.where((item) => item['category'] == 'Shopping').length;

    // Filter items based on selected categories
    final filteredItems = items.where((item) {
      final category = item['category'] as String? ?? 'Cookbooks';
      return categories[category] ?? false;
    }).toList();

    // Group items by category for display
    final cookbookItems =
        filteredItems.where((item) => item['category'] == 'Cookbooks').toList();
    final shoppingItems =
        filteredItems.where((item) => item['category'] == 'Shopping').toList();

    // Build list items with subheadings, including only if category is checked
    final listItems = <dynamic>[
      if (categories['Cookbooks'] == true && cookbookItems.isNotEmpty)
        'Cookbooks Items',
      ...cookbookItems,
      if (categories['Shopping'] == true && shoppingItems.isNotEmpty)
        'Shopping Items',
      ...shoppingItems,
    ];
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: SvgPicture.asset(
            AssetsManager.ic_back, // Placeholder path for your SVG asset
            width: 24,
            height: 24,
          ),
          onPressed: () {
            if (Navigator.of(context).canPop()) {
              Navigator.of(context).pop();
            } else {
              // You can fallback to GoRouter navigation if needed
              context.go(Routes.home);
            }
            // Handle back button press
          },
        ),
        title: Text(
          'Synchronization',
          style: TextStyle(
              fontWeight: FontWeight.w500,
              fontSize: 16,
              color: AppColors.primaryColor),
        ),
        actions: [
          // CustomText(
          //   text: '${filteredItems.length} items',
          //   weight: FontWeight.w500,
          //   size: 14,
          //   color: AppColors.primaryColor,
          // ),
          // Padding for the right side
          IconButton(
            onPressed: () {
              showSyncInfoBottomSheet(context);
              // Handle delete button press
            },
            icon: SvgPicture.asset(AssetsManager.ic_info),
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.only(left: 15, right: 15),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(
              height: 20,
            ),
            CustomText(
              text: 'Welcome Back, Chef!',
              weight: FontWeight.w600,
              size: 20,
              color: AppColors.blackTextColor,
              align: TextAlign.left,
            ),
            SizedBox(
              height: 10,
            ),
            Text(
              'We’ve found your saved recipes, cookbooks, and shopping lists. Sync now to access everything across all your devices — seamlessly and securely.',
              style: TextStyle(
                  fontWeight: FontWeight.w400,
                  fontSize: 14,
                  color: AppColors.texGreyColor),
            ),

            SizedBox(height: 10),
            Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    spreadRadius: 2,
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: categories.entries.map((entry) {
                  return CheckboxListTile(
                    activeColor: AppColors.primaryColor,
                    value: entry.value,
                    onChanged: (val) {
                      syncNotifier.toggleCategory(entry.key, val!);
                    },
                    title: CustomText(
                      text: entry.key,
                      weight: FontWeight.w400,
                      size: 14,
                      color: AppColors.primaryLightTextColor,
                    ),
                    subtitle: CustomText(
                      text:
                          _getSubtitle(entry.key, cookbookCount, shoppingCount),
                      weight: FontWeight.w500,
                      size: 12,
                      color: AppColors.primaryLightTextColor,
                    ),
                    secondary: _getIcon(entry.key),
                    controlAffinity: ListTileControlAffinity.trailing,
                  );
                }).toList(),
              ),
            ),

            const SizedBox(width: 24),

            // Right Column: Sync List
            Expanded(
              child: Container(
                padding: const EdgeInsets.all(5),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 8),
                    Expanded(
                      child: filteredItems.isEmpty
                          ? const Center(
                              child: Text(
                                textAlign: TextAlign.center,
                                'No items to sync. Select a category to view items.',
                                style: TextStyle(
                                  color: AppColors.primaryLightTextColor,
                                  fontWeight: FontWeight.w500,
                                  fontSize: 14,
                                ),
                              ),
                            )
                          : ListView.builder(
                              itemCount: listItems.length,
                              itemBuilder: (context, index) {
                                final item = listItems[index];
                                if (item is String) {
                                  // Subheading
                                  return Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Padding(
                                        padding: const EdgeInsets.symmetric(
                                            vertical: 5),
                                        child: SizedBox(
                                          width: double.infinity,
                                          child: CustomText(
                                            text: item,
                                            weight: FontWeight.w500,
                                            size: 20,
                                            color: AppColors.blackTextColor,
                                          ),
                                        ),
                                      ),
                                      const Divider(),
                                    ],
                                  );
                                } else {
                                  // Item
                                  return Column(
                                    children: [
                                      CheckboxListTile(
                                        activeColor: AppColors.primaryColor,
                                        value: item['isSelected'] ?? false,
                                        onChanged: (val) {
                                          syncNotifier.toggleItem(
                                              item['name'], val!);
                                        },
                                        title: CustomText(
                                          text: item['name'],
                                          weight: FontWeight.w400,
                                          size: 14,
                                          color: AppColors.primaryGreyColor,
                                        ),
                                        subtitle: CustomText(
                                          text: _getItemSubtitle(item),
                                          weight: FontWeight.w400,
                                          size: 12,
                                          color: AppColors.primaryGreyColor,
                                        ),
                                        controlAffinity:
                                            ListTileControlAffinity.trailing,
                                      ),
                                      const Divider(),
                                    ],
                                  );
                                }
                              },
                            ),
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SizedBox(height: 10),

                        CustomText(
                          text: '${filteredItems.length} items',
                          weight: FontWeight.w500,
                          size: 14,
                          color: AppColors.primaryLightTextColor,
                        ),
                        CustomText(
                          text: 'Syncing Recipes',
                          weight: FontWeight.w400,
                          size: 12,
                          color: AppColors.primaryLightTextColor,
                        ),
                        // const SizedBox(height: 5),
                        // CustomText(
                        //   text: progress > 0 ? 'Recipes are being synced...' : 'Ready to sync',
                        //   weight: FontWeight.w400,
                        //   size: 12,
                        //   color: AppColors.primaryLightTextColor,
                        // ),
                        const SizedBox(height: 5),
                        CustomButton(
                          color: Colors.white,
                          textColor: AppColors.primaryColor,
                          text: 'Sync Now',
                          borderRadius: 10,
                          weight: FontWeight.w500,                          onPressed: () {
                            final selectedItemIds = syncNotifier.getSelectedItems();
                            print(
                                'Selected cookbookIds: ${selectedItemIds['cookbookIds']?.length ?? 0}, '
                                    'shoppingListIds: ${selectedItemIds['shoppingListIds']?.length ?? 0}');
                            if( selectedItemIds['cookbookIds']!.isEmpty &&
                                selectedItemIds['shoppingListIds']!.isEmpty) {
                              Utils().showFlushbar(context, message: 'Please select at least one item to sync.', isError: true);
                              return;
                            }
                            syncNotifier.syncSelectedItems(context, selectedItemIds);
                          },
                        ),
                      ],
                    ),

                    SizedBox(
                      height: 10,
                    )
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

Widget _getIcon(String key) {
  switch (key) {
    case 'Cookbooks':
      return SvgPicture.asset(AssetsManager.ic_cookbook,height: 40,width: 40,);
    case 'Shopping':
      return SvgPicture.asset(AssetsManager.ic_shopping,height: 40,width: 40,);
    default:
      return SvgPicture.asset(AssetsManager.ic_cookbook,height: 40,width: 40,);
  }
}

String _getSubtitle(String key, int cookbookCount, int shoppingCount) {
  switch (key) {
    case 'Cookbooks':
      return '$cookbookCount item${cookbookCount == 1 ? '' : 's'} ready to import';
    case 'Shopping':
      return '$shoppingCount item${shoppingCount == 1 ? '' : 's'} ready to import';
    default:
      return '';
  }
}

String _getItemSubtitle(Map<String, dynamic> item) {
  final count = item['count'] ?? 0;
  final status = item['status'] ?? 'Syncing';
  return '$count item${count == 1 ? '' : 's'} • $status';
}

Widget _buildSyncBenefits(BuildContext context) {
  final List<String> features = [
    'Access Anywhere – Your recipes on any device',
    'Instant Updates – Changes reflect everywhere',
    'Cloud Backup – Never lose a recipe',
    'Private & Secure – Encrypted data',
  ];

  return Container(
    width: MediaQuery.of(context).size.width * 0.24,
    padding: const EdgeInsets.all(16),
    decoration: BoxDecoration(
      color: Colors.grey.shade50,
      borderRadius: BorderRadius.circular(16),
    ),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: features.map((feature) {
        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 4.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CustomText(
                text: '•',
                weight: FontWeight.w400,
                size: 14,
                color: AppColors.primaryLightTextColor,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: CustomText(
                  text: feature,
                  weight: FontWeight.w400,
                  size: 14,
                  color: AppColors.primaryLightTextColor,
                ),
              ),
            ],
          ),
        );
      }).toList(),
    ),
  );
}
